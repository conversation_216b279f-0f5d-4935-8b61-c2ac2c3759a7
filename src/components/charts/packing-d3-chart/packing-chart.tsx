import { cn } from '@/lib/styles/utils';

type PackingChartProps = {
  tamValue: string;
  samValue: string;
  somValue: string;
  className?: string;
};

export function PackingChart({
  tamValue,
  somValue,
  samValue,
  className,
}: PackingChartProps) {
  return (
    <div
      className={cn(
        'packing-wrapper flex items-center justify-center',
        className,
      )}
      style={{ fontFamily: 'var(--onest)' }}
    >
      <div className="w-[262px] h-[262px] bg-[#3B4F72] border-[#20365B] border-[5px] rounded-full relative">
        <span className="absolute top-3 left-1/2 transform -translate-x-1/2 font-bold text-white text-sm">
          {tamValue}
        </span>

        <div className="w-[209px] h-[205px] bg-[#96B7D1] border-[#7DA4C2] border-[5px] rounded-full absolute bottom-0 left-1/2 transform -translate-x-1/2">
          <span className="absolute top-3 left-1/2 transform -translate-x-1/2 font-bold text-white text-sm">
            {samValue}
          </span>
          <div className="w-[158px] h-[158px] bg-[#9FD8DD] border-[#7EC5CB] border-[5px] rounded-full absolute bottom-0 left-1/2 transform -translate-x-1/2">
            <span className="absolute top-14 left-1/2 transform -translate-x-1/2 font-bold text-white text-sm">
              {somValue}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
