import { EChartsWrapper } from '@/components';
import { EChartsOption } from 'echarts';
import { CHART_COLORS } from '@/lib/styles/chart-colors';
import { shuffle } from 'lodash';

type PieChartProps = {
  height?: string;
  data: { name: string; value: number }[];
  onClick?: (params: any) => void; // eslint-disable-line
};

export function PieChart({ data, onClick, height }: PieChartProps) {
  const options: EChartsOption = {
    title: {},
    tooltip: {
      trigger: 'item',
    },
    color: shuffle(CHART_COLORS),
    series: [
      {
        name: 'Contract Type',
        type: 'pie',
        radius: '50%',
        data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };

  return (
    <EChartsWrapper
      onClick={onClick}
      option={options}
      style={{ height: height ?? '300px', width: '100%' }}
    />
  );
}
