import { getToken } from 'next-auth/jwt';
import { NextRequest, NextResponse } from 'next/server';

interface DebugInfo {
  environment: {
    KEYCLOAK_ISSUER: string;
    KEYCLOAK_CLIENT_ID: string;
    NEXTAUTH_URL: string;
    NODE_ENV: string | undefined;
  };
  session: {
    hasToken: boolean;
    hasIdToken: boolean;
    hasAccessToken: boolean;
    hasRefreshToken: boolean;
    tokenError: string | null;
  };
  urls: {
    keycloakLogoutEndpoint: string;
    wellKnownConfig: string;
    proposedLogoutUrl?: string;
  };
  idTokenInfo?: {
    issuer?: string;
    audience?: string;
    subject?: string;
    expiry?: string;
    issuedAt?: string;
    sessionId?: string;
    error?: string;
    details?: string;
  };
}

/**
 * Debug endpoint to help troubleshoot federated logout issues
 * Visit /api/auth/debug-logout to see current session and logout URL info
 */
export async function GET(req: NextRequest) {
  try {
    const token = await getToken({ req });
    
    const debugInfo: DebugInfo = {
      environment: {
        KEYCLOAK_ISSUER: process.env.KEYCLOAK_ISSUER || 'NOT_SET',
        KEYCLOAK_CLIENT_ID: process.env.KEYCLOAK_CLIENT_ID || 'NOT_SET',
        NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'NOT_SET',
        NODE_ENV: process.env.NODE_ENV,
      },
      session: {
        hasToken: !!token,
        hasIdToken: !!(token?.['id_token']),
        hasAccessToken: !!(token?.accessToken),
        hasRefreshToken: !!(token?.refreshToken),
        tokenError: token?.error || null,
      },
      urls: {
        keycloakLogoutEndpoint: process.env.KEYCLOAK_ISSUER
          ? `${process.env.KEYCLOAK_ISSUER}/protocol/openid-connect/logout`
          : 'KEYCLOAK_ISSUER_NOT_SET',
        wellKnownConfig: process.env.KEYCLOAK_ISSUER
          ? `${process.env.KEYCLOAK_ISSUER}/.well-known/openid_configuration`
          : 'KEYCLOAK_ISSUER_NOT_SET',
      }
    };

    // If we have a token, show what the logout URL would look like
    if (token && token['id_token']) {
      const logoutParams = new URLSearchParams({
        id_token_hint: token['id_token'] as string,
        post_logout_redirect_uri: process.env.NEXTAUTH_URL || 'NOT_SET',
      });
      
      debugInfo.urls.proposedLogoutUrl = `${debugInfo.urls.keycloakLogoutEndpoint}?${logoutParams}`;
    }

    // If we have an id_token, decode its payload for inspection
    if (token?.['id_token']) {
      try {
        const idTokenPayload = JSON.parse(
          Buffer.from(
            (token['id_token'] as string).split('.')[1], 
            'base64'
          ).toString()
        );
        
        debugInfo.idTokenInfo = {
          issuer: idTokenPayload.iss,
          audience: idTokenPayload.aud,
          subject: idTokenPayload.sub,
          expiry: new Date(idTokenPayload.exp * 1000).toISOString(),
          issuedAt: new Date(idTokenPayload.iat * 1000).toISOString(),
          sessionId: idTokenPayload.sid,
        };
      } catch (decodeError) {
        debugInfo.idTokenInfo = {
          error: 'Failed to decode id_token',
          details: decodeError instanceof Error ? decodeError.message : 'Unknown decode error'
        };
      }
    }

    return NextResponse.json(debugInfo, { 
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Debug endpoint failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
