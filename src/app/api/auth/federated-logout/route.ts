import { getToken, JWT } from 'next-auth/jwt';
import { NextRequest, NextResponse } from 'next/server';

function logoutParams(token: JWT): Record<string, string> {
  const params: Record<string, string> = {};

  // Add id_token_hint if available
  if (token['id_token']) {
    params.id_token_hint = token['id_token'] as string;
  }

  // Add post_logout_redirect_uri
  if (process.env.NEXTAUTH_URL) {
    params.post_logout_redirect_uri = process.env.NEXTAUTH_URL;
  }

  return params;
}

function handleEmptyToken() {
  const response = { error: 'No session present' };
  const responseHeaders = { status: 400 };
  return NextResponse.json(response, responseHeaders);
}

function sendEndSessionEndpointToURL(token: JWT) {
  const endSessionEndPoint = new URL(
    `${process.env.KEYCLOAK_ISSUER}/protocol/openid-connect/logout`,
  );
  const params: Record<string, string> = logoutParams(token);
  const endSessionParams = new URLSearchParams(params);

  // Fix URL construction - don't add extra slash
  const logoutUrl = `${endSessionEndPoint.href}?${endSessionParams}`;

  console.log('Federated logout URL:', logoutUrl);
  console.log('Logout params:', params);

  const response = { url: logoutUrl };
  return NextResponse.json(response);
}

export async function GET(req: NextRequest) {
  try {
    console.log('Federated logout request received');

    // Validate environment variables
    if (!process.env.KEYCLOAK_ISSUER) {
      console.error('KEYCLOAK_ISSUER environment variable is not set');
      return NextResponse.json(
        { error: 'Keycloak configuration missing' },
        { status: 500 }
      );
    }

    if (!process.env.NEXTAUTH_URL) {
      console.error('NEXTAUTH_URL environment variable is not set');
      return NextResponse.json(
        { error: 'NextAuth configuration missing' },
        { status: 500 }
      );
    }

    const token = await getToken({ req });
    console.log('Token retrieved:', {
      hasToken: !!token,
      hasIdToken: !!(token?.['id_token']),
      hasAccessToken: !!(token?.accessToken)
    });

    if (token) {
      return sendEndSessionEndpointToURL(token);
    }
    return handleEmptyToken();
  } catch (error) {
    console.error('Federated logout error:', error);
    const response = {
      error: 'Unable to logout from the session',
      details: error instanceof Error ? error.message : 'Unknown error'
    };
    return NextResponse.json(response, { status: 500 });
  }
}
