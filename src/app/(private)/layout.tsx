import { ReactNode } from 'react';
import type { Metadata } from 'next';
import { getFavoriteList } from '@/app/(private)/(root)/main/favorites/_actions/get-favorite-list';
import { AppStateProvider } from '@/lib/providers/app-state/app-state.provider';

export const metadata: Metadata = {
  title: 'Private HighGround',
};

export default async function PrivateLayout({
  children,
}: {
  children: ReactNode;
}) {
  const favoriteList = await getFavoriteList();

  return (
    <AppStateProvider favorites={favoriteList}>{children}</AppStateProvider>
  );
}
