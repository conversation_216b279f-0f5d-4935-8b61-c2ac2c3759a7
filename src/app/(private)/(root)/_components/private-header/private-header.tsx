import { getServerSession } from 'next-auth';
import { ProfileDropdown } from './components/profile-dropdown';
import { Notifications } from './components/notifications';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/config/nextauth.config';

export async function PrivateHeader() {
  const session = await getServerSession(authOptions);

  // Only redirect if no session or session has error
  if (!session || session.error) {
    redirect('/');
  }

  return (
    <header className="bg-white h-[72px] border-b">
      <nav className="h-full flex px-6 items-center justify-between">
        <div
          className="text-[#262C38] text-xl leading-normal"
          style={{ fontFamily: 'var(--orbitron)' }}
        >
          HighGround
        </div>

        <div className="user-panel flex">
          <Notifications />

          <div className="user-dropdown flex items-center ml-6 pl-6 border-l border-l-[#A9B0BB]">
            {session.user && session.user.name && (
              <ProfileDropdown name={session.user.name} />
            )}
          </div>
        </div>
      </nav>
    </header>
  );
}
