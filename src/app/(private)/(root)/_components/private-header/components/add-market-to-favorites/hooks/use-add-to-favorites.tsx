import { useSWRMutateWithAuth } from '@/lib/hooks/use-swr-mutate-auth';
import { config } from '@/lib/config';

export function useAddToFavorites(marketId: string) {
  const clientUrl = config.api.base_url;
  const { isMutating, error, trigger, data } = useSWRMutateWithAuth(
    `${clientUrl}/dashboard/favorite/${marketId}`,
    'PATCH',
  );

  return { data, error, addToFavorites: trigger, isMutating };
}
