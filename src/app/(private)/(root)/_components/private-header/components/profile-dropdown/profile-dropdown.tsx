import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/dropdown-menu';
import { ProfileIcon } from '@/components/icons/profile-icon';
import { ChevronDown } from 'lucide-react';
import { LogOut } from './log-out';

export function ProfileDropdown({ name }: { name: string }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="flex">
        <ProfileIcon />

        <span className="ml-4 font-medium text-[#262C38]">{name}</span>

        <ChevronDown className="ml-4 text-[#262C38]" />
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        <DropdownMenuItem
          asChild
          className="cursor-pointer"
        >
          <LogOut />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
