import { CSSProperties, ReactNode } from 'react';
import { SidebarProvider } from '@/components';
import MainSideBar from '@/app/(private)/(root)/main/_components/side-bar/main-side-bar';
import { PrivateHeader } from './_components/private-header';
import { SessionGuard } from '@/lib/components/session-guard';

export default async function PrivateRootLayout({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <SessionGuard
      fallback={
        <div className="flex items-center justify-center min-h-screen bg-[#F1F2F2]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 text-lg">Loading your workspace...</p>
          </div>
        </div>
      }
    >
      <div className="private-layout">
        <div className="layout-wrapper">
          <SidebarProvider
            defaultOpen={false}
            style={
              {
                '--sidebar-width': '14rem',
                '--sidebar-width-icon': '81px',
              } as CSSProperties
            }
          >
            <MainSideBar />

            <div className="page-content">
              <div className="w-full">
                <PrivateHeader />

                <div className="layout-content bg-[#F1F2F2] min-h-screen w-full">
                  {children}
                </div>
              </div>
            </div>
          </SidebarProvider>
        </div>
      </div>
    </SessionGuard>
  );
}
