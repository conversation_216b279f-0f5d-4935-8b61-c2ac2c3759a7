'use server';

import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/config/nextauth.config';
import { redirect } from 'next/navigation';
import { config } from '@/lib/config';
import { Favorite } from '@/lib/types/favorite.type';

export async function getFavoriteList(): Promise<Record<string, Favorite[]>> {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/');
  }

  const configBaseUrl = config.api.base_url_for_server_side;
  const url = `${configBaseUrl}/dashboard/favorite`;

  const request = await fetch(url, {
    headers: {
      Authorization: `Bearer ${(session as any)['accessToken']}`, // eslint-disable-line
    },
    // cache: 'force-cache'
  });

  return await request.json();
}
