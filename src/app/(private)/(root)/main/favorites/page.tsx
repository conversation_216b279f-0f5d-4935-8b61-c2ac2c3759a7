import { Separator } from '@/components/separator';
import { Metadata } from 'next';
import { getFavoriteList } from '@/app/(private)/(root)/main/favorites/_actions/get-favorite-list';
import { MarketValue } from '@/app/(private)/(root)/main/market/_lib/types/market.enum';
import Link from 'next/link';
import { v4 as uuidv4 } from 'uuid';

export const metadata: Metadata = {
  title: 'Favorites | HighGround',
};

// Force dynamic rendering for authenticated pages
export const dynamic = 'force-dynamic';

export default async function FavoritesPage() {
  const favorites = await getFavoriteList();

  return (
    <div className="favorites-page py-8 px-8">
      <div className="page-wrapper">
        <div className="page-title mb-4"></div>

        <div className="favorite-dashboards [&>div]:mt-6">
          {!!favorites[MarketValue.VENTURE_CAPITAL] && (
            <div className="venture-capital-favorites">
              <h2 className="font-bold mb-5">
                Venture Capital Market
                <Separator />
              </h2>

              <ul>
                {favorites[MarketValue.VENTURE_CAPITAL].map((item) => (
                  <li key={uuidv4()}>
                    <Link href={item.sourceUrl}>{item.name}</Link>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {!!favorites[MarketValue.PRODUCT_COMPANY] && (
            <div className="product-company-favorites">
              <h2 className="font-bold mb-5">
                Product Company Market <Separator />
              </h2>

              <ul>
                {favorites[MarketValue.PRODUCT_COMPANY].map((item) => (
                  <li key={uuidv4()}>
                    <Link href={item.sourceUrl}>{item.name}</Link>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {!!favorites[MarketValue.HF_PE_Ac] && (
            <div className="venture-capital-favorites">
              <h2 className="font-bold mb-5">
                Private Equity / Hedge Fund / Acquirer <Separator />
              </h2>

              <ul>
                {favorites[MarketValue.HF_PE_Ac].map((item) => (
                  <li key={uuidv4()}>
                    <Link href={item.sourceUrl}>{item.name}</Link>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {!!favorites[MarketValue.GOVERNMENT] && (
            <div className="government-office-favorites">
              <h2 className="font-bold mb-5">
                Government Office <Separator />
              </h2>

              <ul>
                {favorites[MarketValue.GOVERNMENT].map((item) => (
                  <li key={uuidv4()}>
                    <Link href={item.sourceUrl}>{item.name}</Link>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
