import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/config/nextauth.config';
import { redirect } from 'next/navigation';
import { extractRolesFromToken } from '@/lib/utils/jwt-utils';

export default async function DebugRolesPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/');
  }

  let roles: string[] = [];

  if (session.accessToken) {
    try {
      roles = extractRolesFromToken(session.accessToken);
    } catch (error) {
      console.error('Error extracting roles:', error);
    }
  }

  const hasAdminRole = roles.includes('admin');
  const hasUserApproverRole = roles.includes('user-approver');
  const hasAnyAdminAccess = hasAdminRole || hasUserApproverRole;

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Role Debug Information</h1>
      
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">User Information</h2>
          <div className="space-y-2">
            <p><strong>Name:</strong> {session.user?.name || 'N/A'}</p>
            <p><strong>Email:</strong> {session.user?.email || 'N/A'}</p>
            <p><strong>User ID:</strong> {session.user?.id || 'N/A'}</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Role Information</h2>
          <div className="space-y-2">
            <p><strong>All Roles:</strong></p>
            {roles.length > 0 ? (
              <ul className="list-disc list-inside ml-4">
                {roles.map((role, index) => (
                  <li key={index} className="text-sm">{role}</li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-500">No roles found</p>
            )}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Access Control</h2>
          <div className="space-y-2">
            <p><strong>Has Admin Role:</strong> <span className={hasAdminRole ? 'text-green-600' : 'text-red-600'}>{hasAdminRole ? 'Yes' : 'No'}</span></p>
            <p><strong>Has User Approver Role:</strong> <span className={hasUserApproverRole ? 'text-green-600' : 'text-red-600'}>{hasUserApproverRole ? 'Yes' : 'No'}</span></p>
            <p><strong>Has Any Admin Access:</strong> <span className={hasAnyAdminAccess ? 'text-green-600' : 'text-red-600'}>{hasAnyAdminAccess ? 'Yes' : 'No'}</span></p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Session Roles (from NextAuth)</h2>
          <div className="space-y-2">
            <p><strong>Session Roles:</strong></p>
            {session.user?.roles && session.user.roles.length > 0 ? (
              <ul className="list-disc list-inside ml-4">
                {session.user.roles.map((role, index) => (
                  <li key={index} className="text-sm">{role}</li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-500">No roles in session</p>
            )}
          </div>
        </div>

        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">Testing Links</h3>
          <div className="space-y-2">
            <a 
              href="/main/admin/user-approval" 
              className="block text-blue-600 hover:text-blue-800 underline"
            >
              Try to access User Approval page
            </a>
            <a 
              href="/main/home?market=VENTURE_CAPITAL" 
              className="block text-blue-600 hover:text-blue-800 underline"
            >
              Go to Home page
            </a>
          </div>
        </div>
      </div>
    </div>
  );
} 