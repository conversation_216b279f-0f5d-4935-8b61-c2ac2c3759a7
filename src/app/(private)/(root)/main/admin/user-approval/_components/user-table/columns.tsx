'use client';

import { ColumnDef } from '@tanstack/react-table';
import { UserApproval } from '@/app/(private)/(root)/main/admin/user-approval/_lib/types/user-approval';
import { ArrowUpDown, BanIcon, CheckIcon } from 'lucide-react';
import { Button } from '@/components';

export const columns: ColumnDef<UserApproval>[] = [
  {
    accessorKey: 'email',
    header: ({ column }) => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Email
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'username',
    header: ({ column }) => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Username
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'status',
    header: ({ column }) => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'actions',
    header: 'Actions',
    cell: () => {
      return (
        <div className="[&>button+button]:ml-2">
          <Button
            title="Approve User"
            size="sm"
            variant="secondary"
            className="shadow-lg"
          >
            <CheckIcon
              size={14}
              className="text-green-500"
            />
          </Button>

          <Button
            title="Ban User"
            size="sm"
            variant="destructive"
            className="shadow-lg"
          >
            <BanIcon
              size={14}
              className="text-white"
            />
          </Button>
        </div>
      );
    },
  },
];
