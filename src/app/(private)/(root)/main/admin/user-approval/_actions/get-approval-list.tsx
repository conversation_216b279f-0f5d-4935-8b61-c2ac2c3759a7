'use server';

import { authOptions } from '@/lib/config/nextauth.config';
import { getServerSession } from 'next-auth';
import { config } from '@/lib/config';
import { checkAdminAccess } from '@/lib/utils/admin-guard';

export async function getApprovalList() {
  // Check admin access - this will redirect if user doesn't have required roles
  await checkAdminAccess();

  const session = await getServerSession(authOptions);

  if (!session) {
    throw new Error('No session found');
  }

  const configBaseUrl = config.api.base_url_for_server_side;
  const url = `${configBaseUrl}/user`;

  const request = await fetch(url, {
    headers: {
      Authorization: `Bearer ${session.accessToken}`,
    },
    method: 'GET',
  });

  return await request.json();
}
