import { UserApproval } from '@/app/(private)/(root)/main/admin/user-approval/_lib/types/user-approval';
import { UserApprovalTable } from '@/app/(private)/(root)/main/admin/user-approval/_components/user-table/user-approval-table';
import { columns } from '@/app/(private)/(root)/main/admin/user-approval/_components/user-table/columns';
import { getApprovalList } from '@/app/(private)/(root)/main/admin/user-approval/_actions/get-approval-list';
import { checkAdminAccess } from '@/lib/utils/admin-guard';

export default async function UserApprovalPage() {
  // Check admin access - this will redirect if user doesn't have required roles
  await checkAdminAccess();

  const data: UserApproval[] = [
    {
      email: '<EMAIL>',
      username: 'example',
      status: 'pending',
    },
    {
      email: '<EMAIL>',
      username: 'example2',
      status: 'pending',
    },
  ];

  // Call the API to get real data (currently using mock data)
  await getApprovalList();

  return (
    <div className="user-approval-page">
      <UserApprovalTable
        data={data}
        columns={columns}
      />
    </div>
  );
}
