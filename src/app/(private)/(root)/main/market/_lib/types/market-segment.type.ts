import { MarketValue } from '@/app/(private)/(root)/main/market/_lib/types/market.enum';

export type IndustryEntity = {
  id: number;
  legalBusinessName: string;
  sector: string;
};

type InvestmentSeries = {
  id: number;
  name: string;
};

export type MarketSize = {
  tam: number;
  sam: number;
  som: number;
  CAGRCalculation?: string;
};

export type VentureCapitalDetails = {
  description: string;
  totalSpend: number;
  customerTotal: number;
  CAGRCalculation?: number;
  subMarketBreakdown: VentureCapitalService[];
};

export type VentureCapitalService = {
  title: string;
  id: number;
};

export type VentureCapitalMarket = {
  id: number;
  title: string;
  details: VentureCapitalDetails;
  fundingDetails: Record<string, Record<string, number>>;
  marketValue: MarketValue;
};

export type PE_HF_AcMarket = {
  id: number;
  title: string;
  description: string;
  annualGrowthRate: number;
  awards: PE_HF_AcAward[];
  marketSize: MarketSize;
  marketValue: MarketValue;
};

export type PE_HF_AcAward = {
  id: number;
  opportunityTitle: string;
  keyDataIndicators: string[];
  industryEntities: IndustryEntity[];
};

export type ProductCompanyMarket = {
  id: number;
  title: string;
  marketValue: MarketValue;
  productCompanyInvestmentSeries: ProductCompanyInvestmentSeries[];
};

export type ProductCompanyInvestmentSeries = {
  awards: ProductCompanyAward[];
} & InvestmentSeries;

export type ProductCompanyAward = {
  id: number;
  opportunityTitle: string;
  dateSigned: string;
  effectiveDate: string;
};

/**
 * GOVERNMENT OFFICE MARKET
 */
export type GovernmentOfficeMarket = {
  id: number;
  name: string;
};

export type GovernmentOfficeServiceInvestment = {
  id: number;
  name: string;
  programs: GovernmentOfficePrograms[];
};

export type GovernmentOfficePrograms = {
  id: number;
  name: string;
  investmentSeries: GovernmentOfficeInvestmentSeries[];
};

export type GovernmentOfficeInvestmentSeries = {
  awards: GovernmentOfficeAward[];
} & InvestmentSeries;

export type GovernmentOfficeAward = {
  id: number;
  awardTitle: string;
};

/** ENDS
 * GOVERNMENT OFFICE MARKET
 */

export type MarketSegment = {
  name: string;
  description: string;
  totalFunding: number;
  technologyAreas: string[];
};

export type Market = {
  label: string;
  abbr: string;
  value: MarketValue;
};
