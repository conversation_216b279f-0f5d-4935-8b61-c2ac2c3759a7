'use client';

import { PackingChart } from '@/components/charts/packing-d3-chart';
import { cn } from '@/lib/styles/utils';
import { config } from '@/lib/config';
import { useSwrAuth } from '@/lib/hooks/use-swr-auth';
import { toQueryString } from '@/lib/utils/auth-fetch';
import { USACurrency } from '@/lib/config/local-string.config';
import { Skeleton } from '@/components';

export function TamSamSomWidget({
  className,
  marketId,
  series,
}: {
  className?: string;
  marketId: number;
  series: string;
}) {
  const { data, isLoading } = useTamSamSomWidgetRequest(marketId, series);

  const indicator = data && data[0];

  return (
    <>
      {isLoading && <Skeleton className="h-[250px] mb-4" />}

      {!isLoading && !!indicator && (
        <PackingChart
          className={cn(className)}
          tamValue={indicator.tam.toLocaleString('en-US', USACurrency)}
          samValue={indicator.sam.toLocaleString('en-US', USACurrency)}
          somValue={indicator.som.toLocaleString('en-US', USACurrency)}
        />
      )}
    </>
  );
}

function useTamSamSomWidgetRequest(marketId: number, series: string) {
  const apiUrl = config.api.base_url;

  const { data, isLoading, error } = useSwrAuth<
    {
      fiscalYear: string;
      sam: number;
      som: number;
      tam: number;
      series: string;
    }[]
  >(`${apiUrl}/market/${marketId}/sam-tam-som?${toQueryString({ series })}`);

  return { data, isLoading, error };
}
