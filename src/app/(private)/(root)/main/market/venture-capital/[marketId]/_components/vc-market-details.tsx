import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/card';
import { Badge } from '@/components/badge';
import { VentureCapitalMarket } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';

type ContractInformationProps = {
  market: VentureCapitalMarket;
};

export function VcMarketDetails({ market }: ContractInformationProps) {
  return (
    <div className="left-pane-wrapper">
      <Card className="min-h-[450px]">
        <CardHeader>
          <CardTitle className="text-center">Market Details</CardTitle>
        </CardHeader>

        <CardContent>
          <div className="numbers mb-6">
            <h2 className="font-bold italic mb-2">Spending</h2>

            <ul className="[&>li+li]:mt-4">
              <li>
                <span className="flex flex-wrap">
                  <b className="mb-1 text-sm flex-grow shrink-0 basis-full">
                    Total Spend:{' '}
                  </b>

                  <Badge
                    variant="outline"
                    className="grow bg-orange-200"
                  >
                    {market.details.totalSpend.toLocaleString('en-US', {
                      style: 'currency',
                      currency: 'USD',
                    })}
                  </Badge>
                </span>
              </li>

              <li>
                <span className="flex flex-wrap">
                  <b className="mb-1 text-sm flex-grow shrink-0 basis-full">
                    Customer Total:{' '}
                  </b>

                  <Badge
                    variant="outline"
                    className="grow shrink-0 bg-orange-200"
                  >
                    {market.details.customerTotal}
                  </Badge>
                </span>
              </li>
            </ul>
          </div>

          <div className="service-breakdown">
            <h2 className="font-bold italic mb-2">Service Breakdown</h2>

            <ul className="[&>li+li]:mt-4">
              {/*{Object.entries(market.details.servicesBreakdown).map((item) => (*/}
              {/*  <li key={item[0]}>*/}
              {/*    <span className="flex flex-wrap">*/}
              {/*      <b className="mb-1 text-sm flex-grow shrink-0 basis-full">*/}
              {/*        {item[0]}:{' '}*/}
              {/*      </b>*/}

              {/*      <Badge*/}
              {/*        variant="outline"*/}
              {/*        className="grow bg-green-200"*/}
              {/*      >*/}
              {/*        {item[1].toLocaleString('en-US', {*/}
              {/*          style: 'currency',*/}
              {/*          currency: 'USD',*/}
              {/*        })}*/}
              {/*      </Badge>*/}
              {/*    </span>*/}
              {/*  </li>*/}
              {/*))}*/}
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
