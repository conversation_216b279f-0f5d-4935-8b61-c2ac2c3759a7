import { GOSeriesCard } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-series-card/go-series-card';
import { CAGRIndicator } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/cagr-indicator';
import { TamSamSomWidget } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/tam-sam-som-widget';
import { Separator } from '@/components/separator';

type VCSeriesColumnProps = {
  contractTypes: Record<string, number>;
  seriesId: string;
  marketId: number;
  seriesName: string;
};

export function VCSeriesColumn({
  contractTypes,
  seriesName,
  seriesId,
  marketId,
}: VCSeriesColumnProps) {
  return (
    <div className="vc-series-column relative">
      <GOSeriesCard
        contractTypes={contractTypes}
        seriesName={seriesName}
      >
        <Separator className="absolute left-0 right-0" />

        <CAGRIndicator
          className="py-2.5"
          marketId={marketId}
          series={seriesId}
        />

        <TamSamSomWidget
          series={seriesId}
          marketId={marketId}
          className="mx-auto"
        />
      </GOSeriesCard>
    </div>
  );
}
