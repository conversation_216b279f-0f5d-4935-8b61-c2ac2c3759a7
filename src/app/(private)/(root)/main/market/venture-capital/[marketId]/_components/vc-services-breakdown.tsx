import { cn } from '@/lib/styles/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/card';
import { ScrollArea } from '@/components/scroll-area';
import { v4 as uuidv4 } from 'uuid';
import { VentureCapitalService } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';
import { ListIcon } from 'lucide-react';

type VcServiceBreakdownProps = {
  className?: string;
  services: VentureCapitalService[];
};

export function VcServicesBreakdown({
  className,
  services,
}: VcServiceBreakdownProps) {
  return (
    <div className={cn('service-breakdown', className)}>
      <Card className="flex-1 shrink-0 rounded-md shadow-none">
        <CardHeader className="p-4 border-b">
          <CardTitle
            className="text-lg"
            style={{ fontFamily: 'var(--onest)' }}
          >
            <span className="flex items-center gap-x-2">
              <ListIcon />

              <span>Sub-Market Breakdown</span>
            </span>
          </CardTitle>
        </CardHeader>

        <CardContent className="py-4 px-0">
          <ScrollArea className="h-[226px] overflow-auto">
            <ul className="px-4">
              {services.map((item) => (
                <li
                  className="pb-2.5"
                  key={uuidv4()}
                >
                  <span className="flex justify-between">
                    <span>{item.title}</span>

                    <span className="font-bold pl-2">{item.id}%</span>
                  </span>
                </li>
              ))}
            </ul>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
