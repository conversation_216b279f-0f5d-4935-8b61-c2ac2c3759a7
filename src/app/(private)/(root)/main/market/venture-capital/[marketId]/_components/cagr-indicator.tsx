'use client';

import { useSwrAuth } from '@/lib/hooks/use-swr-auth';
import { config } from '@/lib/config';
import { toQueryString } from '@/lib/utils/auth-fetch';
import { Skeleton } from '@/components';
import { ArrowIndicator } from '@/app/(private)/(root)/main/home/<USER>/indicator-card/components/arrow-indicator';
import { cn } from '@/lib/styles/utils';

export function CAGRIndicator({
  marketId,
  series,
  className,
}: {
  marketId: number;
  series: string;
  className?: string;
}) {
  const { data, isLoading } = useCAGRRequest(marketId, series);

  const indicator = data && data[0];
  const finalIndicator = indicator ? indicator.cagr : 0;

  return (
    <div className={cn('cagr-indicator', className)}>
      {isLoading ? (
        <Skeleton className="h-[64px] mb-4" />
      ) : (
        <span className="flex justify-between">
          <span style={{ fontFamily: 'var(--onest)' }}>CAGR</span>

          <span>
            <ArrowIndicator
              arrowIndicator="DOWN"
              arrowValue={finalIndicator + '%'}
            />
          </span>
        </span>
      )}
    </div>
  );
}

function useCAGRRequest(marketId: number, series: string) {
  const apiUrl = config.api.base_url;

  const { data, isLoading, error } = useSwrAuth<
    { cagr: number; series: string }[]
  >(`${apiUrl}/market/${marketId}/cagr?${toQueryString({ series })}`);

  return { data, isLoading, error };
}
