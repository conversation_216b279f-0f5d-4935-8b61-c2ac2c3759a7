import { Skeleton } from '@/components';

export default function Loading() {
  return (
    <div className="main-loading-page py-16 px-8">
      <div className="wrapper pt-8 flex">
        <div className="left-panel max-w-[254px] grow-0 shrink-0 basis-[254px] [&>div+div]:mt-4">
          <Skeleton className="h-[200px]" />

          <Skeleton className="h-[200px]" />

          <Skeleton className="h-[500px]" />
        </div>

        <div className="content flex-1 pl-6">
          <Skeleton className="h-[500px] w-full" />

          <div className="cards mt-4 flex gap-x-4">
            <Skeleton className="h-[284px] grow-0 shrink basis-1/4 max-w-[25%]" />

            <Skeleton className="h-[284px] grow-0 shrink basis-1/4 max-w-[25%]" />

            <Skeleton className="h-[284px] grow-0 shrink basis-1/4 max-w-[25%]" />

            <Skeleton className="h-[284px] grow-0 shrink basis-1/4 max-w-[25%]" />
          </div>
        </div>
      </div>
    </div>
  );
}
