import { notFound } from 'next/navigation';
import { MarketTitleSection } from '@/app/(private)/(root)/main/_components/market-title-section/market-title-section';
import { VcServicesBreakdown } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/vc-services-breakdown';
import { getVentureCapitalDashboardDetails } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_actions/get-vc-dashboard-details';
import { PageParamsProps } from '@/lib/types/page-params-props.type';
import { Metadata } from 'next';
import { headers } from 'next/headers';
import { PageSearchParams } from '@/lib/types/page-search-params';
import { getRandomPercent } from '@/lib/utils/random';
import GOChartSectionLoading from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-chart-section/loading';
import { GOChartSection } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-chart-section/go-chart-section';
import { Suspense } from 'react';
import { formatCurrencyHumanReadable } from '@/lib/config/local-string.config';
import { IndicatorCard } from '../../../home/<USER>/indicator-card/indicator-card';
import { VCSeriesColumn } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/vc-series-column';
import { MarketValue } from '@/app/(private)/(root)/main/market/_lib/types/market.enum';

export const revalidate = 86400; // invalidate every 24 hours

export async function generateMetadata({
  params,
}: PageParamsProps<{ marketId: string }>): Promise<Metadata> {
  const finalParams = await params;
  const marketId = finalParams.marketId;
  const market = await getVentureCapitalDashboardDetails(marketId);

  return {
    title: `${market ? market.title + ' | ' : ''} Venture Capital Dashboard | HighGround`,
    description: market?.details.description,
  };
}

export default async function ContractIdPage({
  params,
}: {
  params: Promise<{ marketId: string }>;
  searchParams: PageSearchParams;
}) {
  const headersList = await headers();
  const path = headersList.get('x-current-path') ?? '';

  const { marketId } = await params;
  const currentMarket = await getVentureCapitalDashboardDetails(marketId);

  if (!currentMarket) {
    notFound();
  }

  return (
    <div className="market-page pb-12 pt-4 px-7 bg-[#F9F9F9]">
      <div className="wrapper">
        <MarketTitleSection
          title={currentMarket.title}
          marketType={MarketValue.VENTURE_CAPITAL}
          marketId={marketId}
          pathToFav={path}
        />

        <div className="flex mb-5">
          <div className="left-pane-venture-market max-w-[360px] grow-0 shrink-0 basis-[360px] [&>div+div]:mt-5">
            <IndicatorCard
              arrowValue="15%"
              name="Total Addressable Market"
              indicator={formatCurrencyHumanReadable(
                currentMarket.details.totalSpend,
              )}
            />

            <div className="flex gap-x-5">
              <IndicatorCard
                name="CAGR"
                className="flex-1 shrink-1"
                arrowIndicator="DOWN"
                arrowValue="29%"
                arrowPosition="CONTENT"
                indicator={
                  (
                    currentMarket.details.CAGRCalculation ?? getRandomPercent()
                  ).toFixed(0) + '%'
                }
                color="secondary"
              />

              <IndicatorCard
                name="Customer Total"
                className="flex-1 shrink-1"
                arrowPosition="CONTENT"
                arrowIndicator="SAME"
                arrowValue="55%"
                indicator={currentMarket.details.customerTotal.toLocaleString(
                  'en-US',
                  {
                    style: 'decimal',
                    notation: 'compact',
                  },
                )}
                color="secondary"
              />
            </div>

            <VcServicesBreakdown
              services={currentMarket.details.subMarketBreakdown}
            />
          </div>

          <div className="venture-market pl-8 grow-0 shrink-0 basis-[calc(100%_-_360px)] max-w-[calc(100%_-_360px)]">
            <Suspense fallback={<GOChartSectionLoading />}>
              <GOChartSection
                programsParams=""
                marketId={marketId}
                servicesParams=""
                className="h-full"
                height="100%"
              />
            </Suspense>
          </div>
        </div>

        <div className="bottom-pane series-market flex gap-x-8 overflow-x-auto">
          <div className="card-container flex-0 shrink-0 max-2xl:basis-[360px] max-2xl:max-w-[360px] 2xl:flex-1 2xl:shrink-1">
            <VCSeriesColumn
              seriesName="Series A"
              seriesId="Early Stage"
              marketId={currentMarket.id}
              contractTypes={currentMarket.fundingDetails['Early Stage'] ?? {}}
            />
          </div>

          <div className="card-container flex-0 shrink-0 max-2xl:basis-[360px] max-2xl:max-w-[360px] 2xl:flex-1 2xl:shrink-1">
            <VCSeriesColumn
              seriesName="Series B"
              seriesId="Mid Stage"
              marketId={currentMarket.id}
              contractTypes={currentMarket.fundingDetails['Mid Stage'] ?? {}}
            />
          </div>

          <div className="card-container flex-0 shrink-0 max-2xl:basis-[360px] max-2xl:max-w-[360px] 2xl:flex-1 2xl:shrink-1">
            <VCSeriesColumn
              seriesName="Series C"
              seriesId="Growth"
              marketId={currentMarket.id}
              contractTypes={currentMarket.fundingDetails['Growth'] ?? {}}
            />
          </div>

          <div className="card-container flex-0 shrink-0 max-2xl:basis-[360px] max-2xl:max-w-[360px] 2xl:flex-1 2xl:shrink-1">
            <VCSeriesColumn
              seriesName="Series D"
              seriesId="Enterprise"
              marketId={currentMarket.id}
              contractTypes={currentMarket.fundingDetails['Enterprise'] ?? {}}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
