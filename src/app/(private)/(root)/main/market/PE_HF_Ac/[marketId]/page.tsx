import { notFound } from 'next/navigation';
import { MarketTitleSection } from '../../../_components/market-title-section';
import { HedgeFundDataTable } from './_components/hedge-fund-data-table';
import { SankeyDiagram } from './_components/sankey-diagram';
import { getHFDashboardDetails } from '@/app/(private)/(root)/main/market/PE_HF_Ac/[marketId]/_actions/get-hf-dashboard-details';
import { PageParamsProps } from '@/lib/types/page-params-props.type';
import { Metadata } from 'next';
import { headers } from 'next/headers';
import { MarketValue } from '@/app/(private)/(root)/main/market/_lib/types/market.enum';

export const revalidate = 86400; // invalidate every 24 hours

export async function generateMetadata({
  params,
}: PageParamsProps<{ marketId: string }>): Promise<Metadata> {
  const finalParams = await params;
  const marketId = finalParams.marketId;
  const market = await getHFDashboardDetails(marketId);

  return {
    title: `${market ? market.title + ' | ' : ''} Private Equity Dashboard | HighGround`,
  };
}

export default async function ContractIdPage({
  params,
}: {
  params: Promise<{ marketId: string }>;
}) {
  const headersList = await headers();
  const path = headersList.get('x-current-path') ?? '';
  const { marketId } = await params;

  const currentMarket = await getHFDashboardDetails(marketId);

  if (!currentMarket) {
    notFound();
  }

  return (
    <div className="market-page pt-4 py-12 px-8 bg-[#F9F9F9]">
      <div className="wrapper">
        <div className="dashboard-header">
          <MarketTitleSection
            title={currentMarket.title}
            pathToFav={path}
            marketId={currentMarket.id + ''}
            marketType={MarketValue.HF_PE_Ac}
          />
        </div>

        <div className="market-details">
          <HedgeFundDataTable contracts={currentMarket.awards} />
        </div>

        <div className="sankey-section mt-6">
          <SankeyDiagram />
        </div>
      </div>
    </div>
  );
}
