import { notFound } from 'next/navigation';
import { MarketTitleSection } from '../../../_components/market-title-section';
import { AwardsTable } from './_components/awards-table';
import { PackingChart } from '@/components/charts/packing-d3-chart';
import { getHFDashboardDetails } from '@/app/(private)/(root)/main/market/PE_HF_Ac/[marketId]/_actions/get-hf-dashboard-details';
import { PageParamsProps } from '@/lib/types/page-params-props.type';
import { Metadata } from 'next';
import { headers } from 'next/headers';
import { MarketValue } from '@/app/(private)/(root)/main/market/_lib/types/market.enum';

export const revalidate = 86400; // invalidate every 24 hours

export async function generateMetadata({
  params,
}: PageParamsProps<{ marketId: string }>): Promise<Metadata> {
  const finalParams = await params;
  const marketId = finalParams.marketId;
  const market = await getHFDashboardDetails(marketId);

  return {
    title: `${market ? market.title + ' | ' : ''} Private Equity Dashboard | HighGround`,
  };
}

export default async function ContractIdPage({
  params,
}: {
  params: Promise<{ marketId: string }>;
}) {
  const headersList = await headers();
  const path = headersList.get('x-current-path') ?? '';
  const { marketId } = await params;

  const currentMarket = await getHFDashboardDetails(marketId);

  if (!currentMarket) {
    notFound();
  }

  return (
    <div className="PE_HF_Ac-page py-16 px-8">
      <div className="wrapper wrapper pt-8">
        <MarketTitleSection
          title={currentMarket.title}
          pathToFav={path}
          marketId={currentMarket.id + ''}
          marketType={MarketValue.HF_PE_Ac}
        />

        <div className="page-content">
          <div className="top-content mx-auto flex mb-10 justify-center gap-x-8 items-center">
            <div className="table w-[650px] h-[300px] bg-gray-400"></div>

            <div className="chart-wrapper">
              <PackingChart
                tamValue={currentMarket.marketSize.tam.toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'USD',
                })}
                samValue={currentMarket.marketSize.sam.toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'USD',
                })}
                somValue={currentMarket.marketSize.som.toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'USD',
                })}
              />
            </div>
          </div>

          <AwardsTable
            className="mx-auto"
            contracts={currentMarket.awards}
          />
        </div>
      </div>
    </div>
  );
}
