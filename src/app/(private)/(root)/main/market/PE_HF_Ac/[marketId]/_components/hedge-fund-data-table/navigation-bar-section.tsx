import { But<PERSON> } from "@/components/button";
import { UncontrolledInput } from "@/components/forms/input";
import { Columns, Tag } from "lucide-react";
import React from "react";

export function NavigationBarSection() {
  return (
    <div className="flex items-center justify-between relative self-stretch w-full flex-[0_0_auto]">
      <div className="flex flex-col w-[350px] items-start gap-1.5 relative">
        <div className="flex items-start gap-2 relative self-stretch w-full flex-[0_0_auto]">
          <div className="flex flex-col items-start gap-1.5 relative flex-1 grow">
            <UncontrolledInput
              placeholder="Filter..."
              className="relative self-stretch w-full flex-[0_0_auto] bg-white border-[#a9b0bb] text-[#a9b0bb] placeholder:text-[#a9b0bb] [font-family:'Inter-Regular',Helvetica] font-normal text-sm"
            />
          </div>
        </div>
      </div>

      <div className="inline-flex items-center justify-end gap-4 relative flex-[0_0_auto]">
        <Button
          variant="outline"
          className="inline-flex items-center justify-center gap-2 px-4 py-2 relative flex-[0_0_auto] bg-white border-[#a9b0bb] h-auto"
        >
          <Tag className="w-4 h-4" />
          <span className="relative w-fit mt-[-1.00px] font-body-medium font-[number:var(--body-medium-font-weight)] text-slate-900 text-[length:var(--body-medium-font-size)] tracking-[var(--body-medium-letter-spacing)] leading-[var(--body-medium-line-height)] whitespace-nowrap [font-style:var(--body-medium-font-style)]">
            Tags
          </span>
        </Button>

        <Button
          variant="outline"
          className="inline-flex items-center justify-center gap-2 px-4 py-2 relative flex-[0_0_auto] bg-white border-[#a9b0bb] h-auto"
        >
          <Columns className="w-4 h-4" />
          <span className="relative w-fit mt-[-1.00px] font-body-medium font-[number:var(--body-medium-font-weight)] text-slate-900 text-[length:var(--body-medium-font-size)] tracking-[var(--body-medium-letter-spacing)] leading-[var(--body-medium-line-height)] whitespace-nowrap [font-style:var(--body-medium-font-style)]">
            Columns
          </span>
        </Button>
      </div>
    </div>
  );
}
