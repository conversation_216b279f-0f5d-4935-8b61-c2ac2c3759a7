'use client';

import { Badge } from "@/components/badge";
import { <PERSON><PERSON> } from "@/components/button";
import { Checkbox } from "@/components/forms/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/table";
import { Building2, <PERSON><PERSON><PERSON>Up, MoreH<PERSON>zon<PERSON> } from "lucide-react";
import React, { useState } from "react";
import { PE_HF_AcAward } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';

interface DataTableSectionProps {
  contracts: PE_HF_AcAward[];
  selectedRows: Set<number>;
  setSelectedRows: (selected: Set<number>) => void;
}

// Transform hedge fund data to match the table design
function transformContractData(contracts: PE_HF_AcAward[]) {
  return contracts.map((contract, index) => ({
    id: contract.id,
    checked: index % 3 === 0, // Some checked for demo
    hasIcon: index % 4 === 0, // Some with icons for demo
    company: contract.industryEntities[0]?.legalBusinessName || 'Unknown Company',
    contracts: contract.opportunityTitle,
    types: contract.keyDataIndicators,
    totalValue: `$${Math.floor(Math.random() * 200 + 10)}M`, // Mock values since not in data
    priority: ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)], // Mock priority
  }));
}

export function DataTableSection({ contracts, selectedRows, setSelectedRows }: DataTableSectionProps) {
  const tableData = transformContractData(contracts);

  const handleRowSelect = (id: number, checked: boolean) => {
    const newSelected = new Set(selectedRows);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedRows(newSelected);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRows(new Set(tableData.map(row => row.id)));
    } else {
      setSelectedRows(new Set());
    }
  };

  const allSelected = tableData.length > 0 && selectedRows.size === tableData.length;
  const someSelected = selectedRows.size > 0 && selectedRows.size < tableData.length;

  return (
    <div className="flex flex-col w-full items-start relative">
      <Table>
        <TableHeader>
          <TableRow className="bg-slate-100 border-slate-200">
            <TableHead className="w-[230px]">
              <div className="flex items-center gap-2">
                <Checkbox 
                  checked={allSelected}
                  ref={(el) => {
                    if (el) el.indeterminate = someSelected;
                  }}
                  onCheckedChange={handleSelectAll}
                  className="w-4 h-4 border-[#3769da] data-[state=checked]:bg-[#3769da]" 
                />
                <span className="[font-family:'Inter-SemiBold',Helvetica] font-semibold text-slate-900 text-sm">
                  Company
                </span>
                <ChevronUp className="w-4 h-4 text-[#3769DA]" />
              </div>
            </TableHead>
            <TableHead className="w-[340px]">
              <div className="flex items-center gap-2">
                <span className="[font-family:'Inter-SemiBold',Helvetica] font-semibold text-slate-900 text-sm">
                  Contracts
                </span>
                <ChevronUp className="w-4 h-4 text-[#A9B0BB]" />
              </div>
            </TableHead>
            <TableHead className="w-[430px]">
              <span className="[font-family:'Inter-SemiBold',Helvetica] font-semibold text-slate-900 text-sm">
                Type
              </span>
            </TableHead>
            <TableHead className="w-[75px] text-center">
              <span className="[font-family:'Inter-SemiBold',Helvetica] font-semibold text-slate-900 text-sm">
                Total Value
              </span>
            </TableHead>
            <TableHead>
              <div className="flex items-center gap-2">
                <span className="[font-family:'Inter-SemiBold',Helvetica] font-semibold text-slate-900 text-sm">
                  Priority
                </span>
                <ChevronUp className="w-4 h-4 text-[#A9B0BB]" />
              </div>
            </TableHead>
            <TableHead className="w-[50px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tableData.map((row) => (
            <TableRow key={row.id} className="h-11 border-slate-200">
              <TableCell>
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={selectedRows.has(row.id)}
                    onCheckedChange={(checked) => handleRowSelect(row.id, checked as boolean)}
                    className="w-4 h-4 border-[#3769da] data-[state=checked]:bg-[#3769da]"
                  />
                  {row.hasIcon && <Building2 className="w-4 h-4" />}
                  <span className="[font-family:'Inter-Regular',Helvetica] font-normal text-slate-900 text-[13px] leading-6">
                    {row.company}
                  </span>
                </div>
              </TableCell>
              <TableCell>
                <span className="[font-family:'Inter-Regular',Helvetica] font-normal text-slate-900 text-[13px]">
                  {row.contracts}
                </span>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1 flex-wrap">
                  {row.types.map((type, index) => (
                    <Badge
                      key={index}
                      className="h-6 px-4 py-0 bg-[#b2d8ff] text-[#4861ae] text-[11px] [font-family:'Inter-SemiBold',Helvetica] font-semibold hover:bg-[#b2d8ff]"
                    >
                      {type}
                    </Badge>
                  ))}
                </div>
              </TableCell>
              <TableCell className="text-center">
                <span className="[font-family:'Inter-Regular',Helvetica] font-normal text-slate-900 text-[13px] leading-6">
                  {row.totalValue}
                </span>
              </TableCell>
              <TableCell>
                <span className="[font-family:'Inter-Regular',Helvetica] font-normal text-slate-900 text-[13px] leading-6">
                  {row.priority}
                </span>
              </TableCell>
              <TableCell>
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-0 w-6 h-6"
                >
                  <MoreHorizontal className="w-6 h-6" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
