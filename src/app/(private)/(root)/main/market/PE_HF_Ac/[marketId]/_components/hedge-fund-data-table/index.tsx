'use client';

import { Card, CardContent } from "@/components/card";
import React, { useState } from "react";
import { DataTableSection } from "./data-table-section";
import { FooterSection } from "./footer-section";
import { NavigationBarSection } from "./navigation-bar-section";
import { PE_HF_AcAward } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';

interface HedgeFundDataTableProps {
  contracts: PE_HF_AcAward[];
}

export function HedgeFundDataTable({ contracts }: HedgeFundDataTableProps) {
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set());

  return (
    <Card className="w-full flex flex-col items-start gap-4 p-6 relative bg-white rounded-[15px] border border-solid border-[#a9b0bb]">
      <CardContent className="flex flex-col w-full items-center gap-4 relative p-0">
        <NavigationBarSection />
        <DataTableSection
          contracts={contracts}
          selectedRows={selectedRows}
          setSelectedRows={setSelectedRows}
        />
        <FooterSection
          selectedCount={selectedRows.size}
          totalCount={contracts.length}
        />
      </CardContent>
    </Card>
  );
}
