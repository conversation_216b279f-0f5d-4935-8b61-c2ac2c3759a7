import { Button } from "@/components/button";
import React from "react";

export function FooterSection() {
  return (
    <footer className="flex h-10 items-center relative self-stretch w-full bg-transparent">
      <div className="px-0 py-2 flex flex-col items-start justify-center relative flex-1 self-stretch grow">
        <div className="flex items-center justify-between relative self-stretch w-full flex-[0_0_auto] mt-[-8.00px] mb-[-8.00px]">
          <div className="flex w-[230px] items-center gap-2 relative">
            <div className="w-fit mt-[-1.00px] [font-family:'Inter-Regular',Helvetica] font-normal text-[#a9b0bb] text-base leading-6 whitespace-nowrap relative tracking-[0]">
              0 of 10 row(s) selected
            </div>
          </div>

          <div className="inline-flex items-center justify-end gap-4 relative flex-[0_0_auto]">
            <Button
              variant="outline"
              size="sm"
              disabled
              className="w-[79px] h-auto px-4 py-2 bg-white border-[#a9b0bb] text-[#a9b0bb] font-body-medium font-[number:var(--body-medium-font-weight)] text-[length:var(--body-medium-font-size)] tracking-[var(--body-medium-letter-spacing)] leading-[var(--body-medium-line-height)] [font-style:var(--body-medium-font-style)]"
            >
              Previous
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="w-[79px] h-auto px-4 py-2 bg-white border-[#3769da] text-[#3769da] font-body-medium font-[number:var(--body-medium-font-weight)] text-[length:var(--body-medium-font-size)] tracking-[var(--body-medium-letter-spacing)] leading-[var(--body-medium-line-height)] [font-style:var(--body-medium-font-style)]"
            >
              Next
            </Button>
          </div>
        </div>
      </div>
    </footer>
  );
}
