'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/card';
import { ScrollArea } from '@/components/scroll-area';
import { GOProgramListItem } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-program-list/go-program-list-item';
import { useSwrAuth } from '@/lib/hooks/use-swr-auth';
import { config } from '@/lib/config';
import { Skeleton } from '@/components';
import { SlidersHorizontalIcon } from 'lucide-react';
import { SelectAllFilters } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/select-all-filters';

export function GOProgramList({
  selectedServices,
  marketId,
}: {
  selectedServices: string[];
  marketId: string;
}) {
  const apiUrl = config.api.base_url;
  const path =
    selectedServices.length > 0
      ? `${apiUrl}/government/market/${marketId}/peos?services=${selectedServices}`
      : `${apiUrl}/government/market/${marketId}/peos`;
  const { data, isLoading } = useSwrAuth<
    {
      id: string;
      name: string;
      totalValue: number;
      availableFundingPercent: number;
    }[]
  >(path);

  return (
    <Card className="w-full bg-[#F4FAFF]">
      <CardHeader className="pt-4 pb-2 px-4">
        <CardTitle>
          <span className="flex justify-between">
            <span className="flex items-center">
              <SlidersHorizontalIcon
                size={15}
                className="mr-2"
              />

              <span
                className="text-lg leading-normal"
                style={{ fontFamily: 'var(--onest)', fontWeight: '500' }}
              >
                Funding Office
              </span>
            </span>

            <SelectAllFilters
              filters={(data ?? [])
                .filter((item) => item.id)
                .map((item) => item.name)}
              filterKey="programs"
            />
          </span>
        </CardTitle>
      </CardHeader>

      <CardContent className="px-0 pb-0">
        <ScrollArea className="h-[226px]">
          <ul className="[&>li+li]:mt-[10px] px-4">
            {!isLoading ? (
              data &&
              data
                .filter((item) => item.id)
                .map((program) => (
                  <GOProgramListItem
                    availableFundingPercent={program.availableFundingPercent}
                    officeId={program.id}
                    name={program.name}
                    marketId={marketId}
                    key={program.id}
                  />
                ))
            ) : (
              <div className="w-full">
                <Skeleton className="w-full h-[40px] mb-2" />
                <Skeleton className="w-full h-[40px] mb-2" />
                <Skeleton className="w-full h-[40px] mb-2" />
                <Skeleton className="w-full h-[40px] mb-2" />
              </div>
            )}

            <li
              className="absolute bottom-0 left-0 right-0  h-[55px] z-10 rounded-b-xl pointer-events-none"
              style={{
                background:
                  'linear-gradient(180deg, rgba(244, 250, 255, 0.1), rgba(244, 250, 255, 1))',
              }}
            ></li>
          </ul>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
