import { TrendingDown, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/styles/utils';

type StateArrowsProps = {
  up: boolean;
  className?: string;
};

export function StateArrows({ up, className }: StateArrowsProps) {
  return (
    <div className={cn('arrow-container', className)}>
      {up ? (
        <TrendingUp className="text-green-500" />
      ) : (
        <TrendingDown className="text-red-500" />
      )}
    </div>
  );
}
