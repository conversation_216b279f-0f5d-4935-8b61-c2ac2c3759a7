'use client';

import { Checkbox } from '@/components/forms/checkbox';
import { useEffect, useRef, useState } from 'react';
import { useSearchParamsManager } from '@/lib/hooks/use-update-search-params';
import { GOProgramListItemContextMenu } from './go-program-list-item-context-menu';
import { GOProgramListItemBBCard } from './go-program-list-item-bb-card';
import { StateArrows } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-program-list/state-arrows';

export function GOProgramListItem({
  name,
  officeId,
  marketId: _marketId, // eslint-disable-line @typescript-eslint/no-unused-vars
  availableFundingPercent,
}: {
  name: string;
  officeId: string;
  marketId: string;
  availableFundingPercent: number;
}) {
  const ref = useRef<HTMLButtonElement>(null);
  const { getParam, updateListParam } = useSearchParamsManager();
  const [checked, setChecked] = useState<boolean>(false);
  const [bbCardOpen, setBBCardOpen] = useState<boolean>(false);

  useEffect(() => {
    let programList = ((getParam('programs') ?? '') as string).split(',');

    if (programList[0] === '') {
      programList = [];
    }

    setChecked(programList.includes(name));
  }, [getParam, name]);

  return (
    <>
      <li
        className="cursor-pointer select-none relative"
        onClick={() => {
          ref.current!.click();
        }}
      >
        <GOProgramListItemContextMenu
          onSelect={() => {
            setBBCardOpen(true);
          }}
        >
          <span className="w-full flex gap-x-1 justify-between items-center">
            <span className="checkbox">
              <Checkbox
                ref={ref}
                checked={checked}
                onCheckedChange={() => {
                  updateListParam('programs', name, 'toggle');
                }}
              />{' '}
              <span>{name}</span>
            </span>

            <StateArrows up={availableFundingPercent > 0.3} />
          </span>
        </GOProgramListItemContextMenu>
      </li>

      <GOProgramListItemBBCard
        sheetTitle={name}
        officeId={officeId}
        open={bbCardOpen}
        onClose={setBBCardOpen}
      />
    </>
  );
}
