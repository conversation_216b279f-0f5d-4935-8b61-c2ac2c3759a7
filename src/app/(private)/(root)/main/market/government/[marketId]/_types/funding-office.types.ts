export interface FundingOfficeAdministrativeDetails {
  officeName: string;
  agencyName: string;
  fundingOfficeCode: string;
}

export interface FundingOfficeFinancialDetails {
  totalContracts: number;
  totalOpportunities: number;
  totalMarkets: number;
  currentUnobligatedFunding: number;
  totalObligation: number;
  totalAwardValue: number;
}

export interface MarketFinancialData {
  year: number;
  totalObligation: number;
  totalAwardValue: number;
}

export interface MarketDetails {
  id: string;
  name: string;
  financialData: MarketFinancialData[];
}

export interface FundingOfficeDetails {
  id: string;
  name: string;
  administrativeDetails: FundingOfficeAdministrativeDetails;
  financialDetails: FundingOfficeFinancialDetails;
  markets: MarketDetails[];
}

// Mock data for development
export const mockFundingOfficeDetails: Record<string, FundingOfficeDetails> = {
  'default': {
    id: 'default',
    name: 'U.S. Air Force - Capabilities Development Management',
    administrativeDetails: {
      officeName: 'U.S. Air Force - Capabilities Development Management',
      agencyName: 'U.S. Air Force',
      fundingOfficeCode: 'AF-CDM-001'
    },
    financialDetails: {
      totalContracts: 1234,
      totalOpportunities: 567,
      totalMarkets: 89,
      currentUnobligatedFunding: 50000000,
      totalObligation: 125000000,
      totalAwardValue: 175000000
    },
    markets: [
      {
        id: 'market-1',
        name: 'Market #1',
        financialData: [
          { year: 2020, totalObligation: 1234, totalAwardValue: 1234 },
          { year: 2021, totalObligation: 1234, totalAwardValue: 1234 },
          { year: 2022, totalObligation: 1234, totalAwardValue: 1234 },
          { year: 2023, totalObligation: 1734, totalAwardValue: 1234 },
          { year: 2024, totalObligation: 1234, totalAwardValue: 1234 },
          { year: 2025, totalObligation: 1234, totalAwardValue: 1234 }
        ]
      },
      {
        id: 'market-2',
        name: 'Market #2',
        financialData: [
          { year: 2020, totalObligation: 2345, totalAwardValue: 2345 },
          { year: 2021, totalObligation: 2345, totalAwardValue: 2345 },
          { year: 2022, totalObligation: 2345, totalAwardValue: 2345 },
          { year: 2023, totalObligation: 2345, totalAwardValue: 2345 },
          { year: 2024, totalObligation: 2345, totalAwardValue: 2345 },
          { year: 2025, totalObligation: 2345, totalAwardValue: 2345 }
        ]
      },
      {
        id: 'market-3',
        name: 'Market #3',
        financialData: [
          { year: 2020, totalObligation: 3456, totalAwardValue: 3456 },
          { year: 2021, totalObligation: 3456, totalAwardValue: 3456 },
          { year: 2022, totalObligation: 3456, totalAwardValue: 3456 },
          { year: 2023, totalObligation: 3456, totalAwardValue: 3456 },
          { year: 2024, totalObligation: 3456, totalAwardValue: 3456 },
          { year: 2025, totalObligation: 3456, totalAwardValue: 3456 }
        ]
      }
    ]
  }
};
