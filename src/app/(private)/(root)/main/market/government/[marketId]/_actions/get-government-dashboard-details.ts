'use server';

import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/config/nextauth.config';
import { Contract } from './filter-contracts';
import { config } from '@/lib/config';
import { redirect } from 'next/navigation';

export async function getGovDashboardDetails(
  id: string,
  programsParam?: string | undefined,
  servicesParams?: string | undefined,
): Promise<{
  marketTitle: string;
  marketDescription: string | null;
  fundingDetails: Record<string, Record<string, number>>;
  contracts: Contract[];
}> {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/');
  }

  const configBaseUrl = config.api.base_url_for_server_side;

  let url = `${configBaseUrl}/market/GOVERNMENT/${id}`;

  if (!!programsParam && !servicesParams) {
    url = url + `?peos=${programsParam}`;
  }

  if (!!servicesParams && !programsParam) {
    url = url + `?services=${servicesParams}`;
  }

  if (!!servicesParams && !!programsParam) {
    url = url + `?services=${servicesParams}&peos=${programsParam}`;
  }

  const request = await fetch(url, {
    headers: {
      Authorization: `Bearer ${(session as any)['accessToken']}`, // eslint-disable-line
    },
    // cache: 'force-cache'
  });

  return await request.json();
}

export async function getGovSeriesContracts(
  marketId: string,
  offices?: string[],
  series?: string[],
): Promise<{
  marketTitle: string;
  marketDescription: string | null;
  fundingDetails: Record<string, Record<string, number>>;
  contracts: Contract[];
}> {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/');
  }

  const configBaseUrl = config.api.base_url_for_server_side;

  // Build query parameters
  const params = new URLSearchParams();
  if (offices && offices.length > 0) {
    params.set('offices', offices.join(','));
  }
  if (series && series.length > 0) {
    params.set('series', series.join(','));
  }

  const queryString = params.toString();
  const url = `${configBaseUrl}/government/market/${marketId}/series/contracts${queryString ? `?${queryString}` : ''}`;

  const request = await fetch(url, {
    headers: {
      Authorization: `Bearer ${(session as any)['accessToken']}`, // eslint-disable-line
    },
  });

  return await request.json();
}
