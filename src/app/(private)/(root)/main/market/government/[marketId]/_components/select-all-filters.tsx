'use client';

import { Button } from '@/components';
import { useSearchParamsManager } from '@/lib/hooks/use-update-search-params';
import { isEqual } from 'lodash';

export function SelectAllFilters({
  filters,
  filterKey,
}: {
  filters: string[];
  filterKey: string;
}) {
  const { updateListParam, getListParam } = useSearchParamsManager();

  const handleSelectAllFilters = () => {
    updateListParam(filterKey, filters, 'removeAllAndAdd');
  };

  if (filters.length === 0) {
    return <></>;
  }

  const currentList = getListParam(filterKey);

  const hasAllSelected = isEqual(filters, currentList);

  const removeAllSelectedFilters = () => {
    updateListParam(filterKey, filters, 'remove');
  };

  return (
    <>
      {hasAllSelected ? (
        <Button
          variant="link"
          onClick={() => removeAllSelectedFilters()}
          className="text-sm text-[#017EFA] pr-0"
        >
          Remove All
        </Button>
      ) : (
        <Button
          variant="link"
          onClick={() => handleSelectAllFilters()}
          className="text-sm text-[#017EFA] pr-0"
        >
          Select All
        </Button>
      )}
    </>
  );
}
