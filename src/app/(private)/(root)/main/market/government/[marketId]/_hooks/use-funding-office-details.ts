'use client';

import { useSwrAuth } from '@/lib/hooks/use-swr-auth';
import { config } from '@/lib/config';

export type GovernmentOfficeDetails = {
  officeName: string;
  agencyName: string;
  fundingOfficeCode: string;
  totalContracts: number;
  totalOpportunities: number;
  totalMarkets: number;
  currentUnobligatedFunding: number;
  totalObligation: number;
  totalAwardValue: number;
  yearlyOverview: {
    year: string;
    totalObligation: number;
    totalAwardValue: number;
  }[];
};

export function useFundingOfficeDetails(officeId: string) {
  const apiUrl = config.api.base_url;
  const path = `${apiUrl}/government/market/office/details?office=${encodeURIComponent(officeId)}`;

  const { data, isLoading, error } = useSwrAuth<GovernmentOfficeDetails>(path);

  return {
    data,
    isLoading,
    error
  };
}
