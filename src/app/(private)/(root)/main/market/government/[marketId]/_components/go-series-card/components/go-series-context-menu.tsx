import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from '@/components/context-menu';
import { ReactNode } from 'react';

export function GOSeriesContextMenu({ children }: { children: ReactNode }) {
  return (
    <ContextMenu>
      <ContextMenuTrigger>{children}</ContextMenuTrigger>
      <ContextMenuContent>
        <ContextMenuItem>Add to Watchlist</ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
}
