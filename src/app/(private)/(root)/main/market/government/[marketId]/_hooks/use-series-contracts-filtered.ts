'use client';

import { useSwrAuth } from '@/lib/hooks/use-swr-auth';
import { config } from '@/lib/config';
import { Contract } from '../_actions/filter-contracts';

export type SeriesContractsData = {
  marketTitle: string;
  marketDescription: string | null;
  fundingDetails: Record<string, Record<string, number>>;
  contracts: Contract[];
};

export function useSeriesContractsFiltered(
  marketId: string,
  offices?: string[],
  series?: string[],
) {
  const apiUrl = config.api.base_url;
  
  // Build query parameters
  const params = new URLSearchParams();
  if (offices && offices.length > 0) {
    params.set('offices', offices.join(','));
  }
  if (series && series.length > 0) {
    params.set('series', series.join(','));
  }

  const queryString = params.toString();
  const url = `${apiUrl}/government/market/${marketId}/series/contracts${queryString ? `?${queryString}` : ''}`;

  const { data, isLoading, error } = useSwrAuth<SeriesContractsData>(url);

  return {
    data,
    isLoading,
    error,
  };
}
