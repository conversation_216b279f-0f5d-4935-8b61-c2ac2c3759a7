'use client';

import { Checkbox } from '@/components/forms/checkbox';
import { useEffect, useRef, useState } from 'react';
import { useSearchParamsManager } from '@/lib/hooks/use-update-search-params';
import { cn } from '@/lib/styles/utils';

export function GOServiceListItem({
  name,
  value,
}: {
  name: string;
  value: string;
}) {
  const ref = useRef<HTMLButtonElement>(null);
  const { getParam, batchUpdateParams } = useSearchParamsManager();
  const [checked, setChecked] = useState<boolean>(false);

  useEffect(() => {
    let serviceList = ((getParam('services') ?? '') as string).split(',');

    if (serviceList[0] === '') {
      serviceList = [];
    }

    setChecked(serviceList.includes(name));
  }, [getParam, name]);

  return (
    <li
      className={cn(
        'flex flex-wrap cursor-pointer select-none',
        checked && 'font-bold',
      )}
      onClick={() => {
        ref.current!.click();
      }}
    >
      <span
        className={cn(
          'flex-0 shrink-0 basis-3/4 max-w-[75%] overflow-hidden text-ellipsis',
        )}
      >
        <Checkbox
          ref={ref}
          checked={checked}
          onCheckedChange={() => {
            batchUpdateParams((params) => {
              params.delete('programs');

              const current =
                params.get('services')?.split(',').filter(Boolean) ?? [];

              const exists = current.includes(name);
              const updated = exists
                ? current.filter((item) => item !== name)
                : [...current, name];

              if (updated.length > 0) {
                params.set('services', updated.join(','));
              } else {
                params.delete('services');
              }
            });
          }}
        />{' '}
        {name}
      </span>

      <span className="flex-0 shrink-0 basis-1/4 max-w-[25%] text-right">
        {value}
      </span>
    </li>
  );
}
