import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from '@/components/sheet';
import { ReactNode } from 'react';
import { Contract } from '@/app/(private)/(root)/main/market/government/[marketId]/_actions/filter-contracts';

export function GOSeriesBaseballCard({
  children,
  award,
}: {
  children: ReactNode;
  award: Contract;
}) {
  return (
    <Sheet>
      <SheetTrigger
        asChild
        className="p-0"
      >
        <button className="cursor-pointer p-0 text-sm block w-full text-left">
          {children}
        </button>
      </SheetTrigger>
      <SheetContent side="right_within">
        <SheetHeader>
          <SheetTitle className="mb-6">
            ContractId: {award.contractId}
          </SheetTitle>

          <ul className="pl-6 list-disc">
            <li className="mb-2">
              <b>Total Value:</b>{' '}
              {award.totalValue.toLocaleString('en-US', {
                style: 'currency',
                currency: 'USD',
                notation: 'compact',
              })}
            </li>

            <li className="mb-2">
              <b>Awarding Office:</b> {award.awardingOffice}
            </li>

            <li className="mb-2">
              <b>Award Year:</b> {award.actionDate}
            </li>
          </ul>
        </SheetHeader>
      </SheetContent>
    </Sheet>
  );
}
