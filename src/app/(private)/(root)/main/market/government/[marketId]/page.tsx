import { getGovDashboardDetails } from '@/app/(private)/(root)/main/market/government/[marketId]/_actions/get-government-dashboard-details';
import { Metadata } from 'next';
import { PageParamsProps } from '@/lib/types/page-params-props.type';
import { headers } from 'next/headers';
import { GovernmentDashboardClient } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/government-dashboard-client';

export const revalidate = 86400; // invalidate every 24 hours

export async function generateMetadata({
  params,
}: PageParamsProps<{ marketId: string }>): Promise<Metadata> {
  const finalParams = await params;
  const marketId = finalParams.marketId;
  const market = await getGovDashboardDetails(marketId);

  return {
    title: `${market.marketTitle} | Government Dashboard | HighGround`,
    description: market.marketDescription,
  };
}

export default async function GovernmentOfficePage({
  params,
  searchParams,
}: {
  params: Promise<{ marketId: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const headersList = await headers();
  const path = headersList.get('x-current-path') ?? '';
  const finalSearchParams = await searchParams;
  const finalParams = await params;
  const marketId = finalParams.marketId;

  const market = await getGovDashboardDetails(
    marketId,
    finalSearchParams['programs'] as string,
    finalSearchParams['services'] as string,
  );

  return (
    <GovernmentDashboardClient
      marketId={marketId}
      initialData={{
        marketTitle: market.marketTitle,
        marketDescription: market.marketDescription,
        fundingDetails: market.fundingDetails,
      }}
      path={path}
    />
  );
}
