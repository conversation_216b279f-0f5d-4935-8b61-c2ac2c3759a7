import { config } from '@/lib/config';
import { useSWRMutateWithAuth } from '@/lib/hooks/use-swr-mutate-auth';

export function useSeriesContracts() {
  const configBaseUrl = config.api.base_url;
  const url = `${configBaseUrl}/government/market/contracts`;

  const { data, trigger, isMutating, error } = useSWRMutateWithAuth(url, 'GET');

  return { data, getContractsByType: trigger, isMutating, error };
}
