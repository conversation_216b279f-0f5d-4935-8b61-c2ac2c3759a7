'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/card';
import { cn } from '@/lib/styles/utils';
import { GOServiceListItem } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-service-list/go-service-list-item';
import { useSwrAuth } from '@/lib/hooks/use-swr-auth';
import { config } from '@/lib/config';
import { Skeleton } from '@/components';
import { ScrollArea } from '@/components/scroll-area';
import { SlidersHorizontalIcon } from 'lucide-react';
import { USACurrency } from '@/lib/config/local-string.config';
import { SelectAllFilters } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/select-all-filters';

type GOServiceListProps = {
  className?: string;
  marketId: string;
};

export function GOServiceList({ marketId, className }: GOServiceListProps) {
  const apiUrl = config.api.base_url;

  const { data, isLoading } = useSwrAuth<
    { id: string; name: string; totalValue: number }[]
  >(`${apiUrl}/government/market/${marketId}/services`);

  return (
    <Card className={cn('w-full bg-[#F4FAFF]', className)}>
      <CardHeader className="pt-4 pb-2 px-4">
        <CardTitle>
          <span className="flex justify-between">
            <span className="flex items-center">
              <SlidersHorizontalIcon
                size={15}
                className="mr-2"
              />

              <span
                className="text-lg leading-normal"
                style={{ fontFamily: 'var(--onest)', fontWeight: '500' }}
              >
                Service Investment
              </span>
            </span>

            {data && (
              <SelectAllFilters
                filterKey="services"
                filters={(data ?? []).map((item) => item.name)}
              />
            )}
          </span>
        </CardTitle>
      </CardHeader>

      <CardContent className="px-0 pb-0">
        <ScrollArea className="h-[226px]">
          <ul className="[&>li+li]:mt-[10px] px-4">
            {!isLoading ? (
              data &&
              data.map(({ id, name, totalValue }) => (
                <GOServiceListItem
                  key={id}
                  name={name}
                  value={totalValue.toLocaleString('en-US', USACurrency)}
                />
              ))
            ) : (
              <div className="w-full">
                <Skeleton className="w-full h-[40px] mb-2" />
                <Skeleton className="w-full h-[40px] mb-2" />
                <Skeleton className="w-full h-[40px] mb-2" />
                <Skeleton className="w-full h-[40px] mb-2" />
              </div>
            )}

            <li
              className="absolute bottom-0 left-0 right-0  h-[55px] z-10 rounded-b-xl pointer-events-none"
              style={{
                background:
                  'linear-gradient(180deg, rgba(244, 250, 255, 0.1), rgba(244, 250, 255, 1))',
              }}
            ></li>
          </ul>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
