'use client';

import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetT<PERSON>le,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/table';
import { useFundingOfficeDetails } from '../../_hooks/use-funding-office-details';

export function GOProgramListItemBBCard({
  open,
  onClose,
  sheetTitle,
  officeId,
}: {
  sheetTitle: string;
  officeId: string;
  open: boolean;
  onClose: (open: boolean) => void;
}) {
  const { data: fundingOfficeDetails, isLoading } = useFundingOfficeDetails(officeId);

  if (isLoading || !fundingOfficeDetails) {
    return null;
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  return (
    <Sheet
      open={open}
      modal={true}
      onOpenChange={onClose}
    >
      <SheetContent side="right_within" className="w-[800px] max-w-[90vw]">
        <SheetHeader>
          <SheetTitle className="mb-6">{sheetTitle}</SheetTitle>
          <SheetDescription className="sr-only">Funding Office Details</SheetDescription>
        </SheetHeader>

        <div className="-mx-6">
          {/* Administrative Details */}
          <div className="bg-blue-50 border-b border-blue-100 px-8 py-4">
            <h3 className="text-blue-700 text-base font-medium mb-3">Administrative Details</h3>
            <div className="space-y-1 text-sm text-slate-900">
              <div className="leading-6">
                Office Name: {fundingOfficeDetails.officeName}
              </div>
              <div className="leading-6">
                Agency Name: {fundingOfficeDetails.agencyName}
              </div>
              <div className="leading-6">
                Funding Office Code: {fundingOfficeDetails.fundingOfficeCode}
              </div>
            </div>
          </div>

          {/* Financial Details */}
          <div className="bg-slate-50 border-b border-slate-200 px-8 py-4">
            <h3 className="text-blue-700 text-base font-medium mb-3">Financial Details (Current this year)</h3>
            <div className="space-y-1 text-sm text-slate-900">
              <div className="leading-6">
                Total Contracts: {formatNumber(fundingOfficeDetails.totalContracts)}
              </div>
              <div className="leading-6">
                Total Opportunities: {formatNumber(fundingOfficeDetails.totalOpportunities)}
              </div>
              <div className="leading-6">
                Total Markets: {formatNumber(fundingOfficeDetails.totalMarkets)}
              </div>
              <div className="leading-6">
                Current Unobligated Funding: {formatCurrency(fundingOfficeDetails.currentUnobligatedFunding)}
              </div>
              <div className="leading-6">
                Total Obligation: {formatCurrency(fundingOfficeDetails.totalObligation)}
              </div>
              <div className="leading-6">
                Total Award Value: {formatCurrency(fundingOfficeDetails.totalAwardValue)}
              </div>
            </div>
          </div>

          {/* Market Data */}
          <div className="bg-white px-8 pt-4 pb-8">
            <Tabs defaultValue="market-data" className="w-full">
              <TabsList className="w-full justify-center">
                <TabsTrigger value="market-data" className="flex-1 max-w-[120px]">
                  Market Data
                </TabsTrigger>
              </TabsList>

              <TabsContent value="market-data" className="mt-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-sm font-semibold text-slate-900">Year</TableHead>
                      <TableHead className="text-sm font-semibold text-slate-900">Total Obligation</TableHead>
                      <TableHead className="text-sm font-semibold text-slate-900">Total Award Value</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {fundingOfficeDetails.yearlyOverview.map((data: { year: string; totalObligation: number; totalAwardValue: number }) => (
                      <TableRow key={data.year}>
                        <TableCell className="text-[13px] font-normal text-slate-900">{data.year}</TableCell>
                        <TableCell className="text-[13px] font-normal text-slate-900">{formatCurrency(data.totalObligation)}</TableCell>
                        <TableCell className="text-[13px] font-normal text-slate-900">{formatCurrency(data.totalAwardValue)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
