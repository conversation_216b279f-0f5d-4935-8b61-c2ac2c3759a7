import { ArrowDownIcon, ArrowUpIcon, MinusIcon } from 'lucide-react';
import { cn } from '@/lib/styles/utils';

export function ArrowIndicator({
  arrowValue,
  arrowIndicator,
  className,
}: {
  arrowValue?: string;
  className?: string;
  arrowIndicator?: 'UP' | 'DOWN' | 'SAME';
}) {
  return (
    <span
      className={cn(
        'flex items-center',
        arrowIndicator === 'UP' && 'text-[#45B361]',
        arrowIndicator === 'DOWN' && 'text-[#DC2625]',
        arrowIndicator === 'SAME' && 'text-[#d9c000]',
        className,
      )}
      style={{fontFamily: 'var(--onest)'}}
    >
      <span
        className={cn(
          'inline-flex items-center justify-center circle rounded-full h-[20px] w-[20px]',
          arrowIndicator === 'UP' && 'bg-[#DEF7E4]',
          arrowIndicator === 'DOWN' && 'bg-[#FEE2E1]',
          arrowIndicator === 'SAME' && 'bg-[#fcf5c1]',
        )}
      >
        {arrowIndicator === 'UP' && <ArrowUpIcon size={16} />}

        {arrowIndicator === 'DOWN' && <ArrowDownIcon size={16} />}

        {arrowIndicator === 'SAME' && <MinusIcon size={16} />}
      </span>

      <span className="ml-2">{!!arrowValue && <span>{arrowValue}</span>}</span>
    </span>
  );
}
