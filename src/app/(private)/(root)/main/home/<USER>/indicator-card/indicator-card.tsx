import { Card, CardContent, CardHeader, CardTitle } from '@/components/card';
import { cn } from '@/lib/styles/utils';
import { ArrowIndicator } from './components/arrow-indicator';

type IndicatorCardProps = {
  name: string;
  indicator: string;
  arrowIndicator?: 'UP' | 'DOWN' | 'SAME';
  arrowValue?: string;
  className?: string;
  arrowPosition?: 'TITLE' | 'CONTENT';
  color?: 'primary' | 'secondary';
};

export function IndicatorCard({
  className,
  indicator,
  arrowPosition = 'TITLE',
  arrowValue,
  arrowIndicator = 'UP',
  name,
  color = 'primary',
}: IndicatorCardProps) {
  return (
    <Card
      className={cn('rounded-md shadow-none', className)}
      style={{ fontFamily: 'var(--onest)' }}
    >
      <CardHeader className="pt-2 pb-2 px-4">
        <CardTitle
          className={cn(
            'flex justify-between text-[#64748B] text-lg font-normal',
          )}
        >
          <span>{name}</span>

          {arrowPosition === 'TITLE' && !!arrowValue && (
            <ArrowIndicator
              className="mr-2"
              arrowIndicator={arrowIndicator}
              arrowValue={arrowValue}
            />
          )}
        </CardTitle>
      </CardHeader>

      <CardContent
        className={cn(
          'pb-2 px-4 text-[40px] font-bold',
          color === 'primary' ? 'text-[#262C38]' : 'text-slate-700',
        )}
      >
        <span className="flex items-center justify-between">
          <span>{indicator}</span>

          {arrowPosition === 'CONTENT' && !!arrowValue && (
            <ArrowIndicator
              className="ml-2"
              arrowIndicator={arrowIndicator}
            />
          )}
        </span>
      </CardContent>
    </Card>
  );
}
