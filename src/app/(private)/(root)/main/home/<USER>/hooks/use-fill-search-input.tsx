import { useEffect, useRef } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { MarketValue } from '@/app/(private)/(root)/main/market/_lib/types/market.enum';

/**
 * Updates the queryParams when the search box changes
 * @param market
 * @param searchBoxQuery
 */
export function useFillSearchInput(market: string, searchBoxQuery: string) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const firstRender = useRef(false);

  useEffect(() => {
    const newSearchParams = new URLSearchParams(searchParams);
    const marketValue = market ?? MarketValue.VENTURE_CAPITAL;
    newSearchParams.set('market', marketValue);
    router.push(`${pathname}?${newSearchParams}`);
  }, [market]); // eslint-disable-line

  useEffect(() => {
    if (!firstRender.current && searchParams.size === 0) {
      return;
    }

    const debouncedInputTimeout = setTimeout(() => {
      const newSearchParams = new URLSearchParams(searchParams);
      const marketQueryParam = searchParams.get('market');

      if (!marketQueryParam) {
        newSearchParams.set('market', market ?? MarketValue.VENTURE_CAPITAL);
      }

      newSearchParams.set('search', searchBoxQuery ?? '');
      router.push(`${pathname}?${newSearchParams}`);
    }, 1000);

    return () => {
      clearTimeout(debouncedInputTimeout);
    };
  }, [searchBoxQuery]); // eslint-disable-line

  useEffect(() => {
    firstRender.current = true;
  }, []);
}
