'use client';

import { cn } from '@/lib/styles/utils';
import { SearchBox } from './search-box';
import { Form } from '@/components/forms/form';
import { useForm, useWatch } from 'react-hook-form';
import { getMarketByValue } from './market-dropdown';
import { useSearchParams } from 'next/navigation';
import { useFillSearchInput } from '../_lib/hooks/use-fill-search-input';
import { useEffect } from 'react';

type SearchFormProps = {
  className?: string;
};

type SearchForm = {
  searchBoxQuery?: string;
  market?: string;
};

export function SearchForm({ className }: SearchFormProps) {
  const searchParams = useSearchParams();
  const marketFromParams = searchParams.get('market') ?? '';
  const form = useForm<SearchForm>({
    defaultValues: {
      searchBoxQuery: searchParams.get('search') ?? '',
      market: getMarketByValue(marketFromParams).value,
    },
  });
  const { searchBoxQuery, market } = useWatch({ ...form });

  useEffect(() => {
    // Only update the form if there's actually a market parameter in the URL
    if (marketFromParams) {
      form.setValue('market', marketFromParams);
    }
  }, [marketFromParams]) // eslint-disable-line

  useFillSearchInput(market ?? 'VENTURE_CAPITAL', searchBoxQuery ?? '');

  return (
    <div className={cn('search-form', className)}>
      <Form {...form}>
        <SearchBox name="searchBoxQuery" />
      </Form>
    </div>
  );
}
