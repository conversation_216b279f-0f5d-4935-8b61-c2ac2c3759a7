'use client';

import { SearchResultsList } from '@/app/(private)/(root)/main/home/<USER>/search-results/search-results-list';
import { useSearchParams } from 'next/navigation';
import { MarketValue } from '@/app/(private)/(root)/main/market/_lib/types/market.enum';

export function SearchResults() {
  const searchParams = useSearchParams();

  // Provide fallback to VENTURE_CAPITAL if no market is specified
  const marketQuery = searchParams.get('market') || MarketValue.VENTURE_CAPITAL;
  const searchQuery = searchParams.get('search') ?? '';

  return (
    <div className="search-results">
      <SearchResultsList
        searchQuery={searchQuery}
        marketQuery={marketQuery}
      />
    </div>
  );
}
