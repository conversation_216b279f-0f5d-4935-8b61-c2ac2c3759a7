import { GovernmentOfficeMarket } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';
import { MarketValue } from '@/app/(private)/(root)/main/market/_lib/types/market.enum';
import { config } from '@/lib/config';
import { useSwrAuth } from '@/lib/hooks/use-swr-auth';
import {
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/command';
import Link from 'next/link';
import { Loader2 } from 'lucide-react';
import { SessionGuard } from '@/lib/components/session-guard';

type SearchResultsListProps = {
  searchQuery: string;
  marketQuery: string;
};

function getPathByMarket(market: MarketValue): string {
  switch (market) {
    case MarketValue.VENTURE_CAPITAL:
      return 'venture-capital';
    case MarketValue.PRODUCT_COMPANY:
      return 'product-company';
    case MarketValue.HF_PE_Ac:
      return 'PE_HF_Ac';
    case MarketValue.GOVERNMENT:
      return 'government';
  }
}

export function SearchResultsList({
  searchQuery,
  marketQuery,
}: SearchResultsListProps) {
  const newConfig = config.api.base_url;

  // Only make API request when searchQuery is not empty
  const shouldFetch = searchQuery && searchQuery.trim().length > 0;

  // Construct the API URL
  const apiUrl = shouldFetch ? `${newConfig}/market/${marketQuery}?searchTerm=${searchQuery}` : null;

  const { data, isLoading } = useSwrAuth<GovernmentOfficeMarket[]>(apiUrl);

  return (
    <SessionGuard
      fallback={
        <CommandList>
          <div className="flex justify-center p-4">
            <Loader2 className="animate-spin" />
          </div>
        </CommandList>
      }
    >
      <CommandList>
        {!!searchQuery && data && data?.length === 0 && (
          <CommandEmpty>No results found.</CommandEmpty>
        )}

        {!!searchQuery && isLoading && (
          <div className="flex justify-center">
            <Loader2 className="animate-spin" />
          </div>
        )}

        {!!searchQuery && data && (
          <CommandGroup heading="Search Results">
            {data.map((item) => (
              <CommandItem
                className="cursor-pointer"
                key={item.id}
                asChild
              >
                <Link
                  href={{
                    pathname: `/main/market/${getPathByMarket(marketQuery as MarketValue)}/${item.id}`,
                  }}
                >
                  {item.name}
                </Link>
              </CommandItem>
            ))}
          </CommandGroup>
        )}
      </CommandList>
    </SessionGuard>
  );
}
