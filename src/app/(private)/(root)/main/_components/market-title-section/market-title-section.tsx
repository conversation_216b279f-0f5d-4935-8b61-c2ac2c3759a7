'use client';

import { AddMarketToFavorites } from '@/app/(private)/(root)/_components/private-header/components/add-market-to-favorites/add-market-to-favorites';
import { ChevronLeft } from 'lucide-react';
import { Button } from '@/components';
import Link from 'next/link';
import { MarketValue } from '@/app/(private)/(root)/main/market/_lib/types/market.enum';

export function MarketTitleSection({
  title,
  pathToFav,
  marketType,
  marketId,
}: {
  title: string;
  pathToFav?: string;
  marketType: MarketValue;
  marketId: string;
}) {
  return (
    <div className="dashboard-title mb-5">
      <div className="back-to-home">
        <Button
          variant="ghost"
          asChild
          className="text-xs text-[#64748B] px-0 gap-0.5 items-center"
        >
          <Link href="/main/home?market=VENTURE_CAPITAL">
            <ChevronLeft size={15} />

            <span>Back to Home</span>
          </Link>
        </Button>
      </div>

      <div className="title flex justify-between items-center">
        <h1
          className="text-2xl font-semibold"
          style={{ fontFamily: 'var(--onest)' }}
        >
          {title}
        </h1>

        <div className="tools">
          {pathToFav && (
            <AddMarketToFavorites
              marketId={marketId}
              marketType={marketType}
              className="ml-auto"
              pathToFav={pathToFav}
            />
          )}
        </div>
      </div>
    </div>
  );
}
