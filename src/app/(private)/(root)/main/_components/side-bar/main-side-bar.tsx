'use client';

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components';
import Link from 'next/link';
import { HomeIcon, RssIcon, StarIcon, UserCheckIcon, BugIcon } from 'lucide-react';
import { cn } from '@/lib/styles/utils';
import { HighGroundLogo } from '@/components/icons/high-ground-logo';
import { RoleGuard } from '@/lib/components/role-guard';

type MainSideBarProps = {
  className?: string;
};

export default function MainSideBar({ className }: MainSideBarProps) {
  return (
    <aside className={cn('', className)}>
      <Sidebar
        className="peer"
        collapsible="icon"
      >
        <SidebarHeader className="mb-4 pt-6">
          <SidebarMenu>
            <SidebarMenuItem className="flex justify-center">
              <SidebarMenuButton
                asChild
                className="[&>svg]:size-8"
              >
                <Link
                  href="/main/home?market=VENTURE_CAPITAL&search="
                  className="hover:!bg-transparent flex justify-center"
                >
                  <HighGroundLogo />
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
          <SidebarGroup className="pt-6">
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem className="flex justify-center">
                  <SidebarMenuButton
                    asChild
                    className="[&>svg]:size-6 group-data-[collapsible=icon]:[&>svg]:-ml-1"
                  >
                    <Link
                      href="/main/home?market=VENTURE_CAPITAL"
                      title="Home"
                      className="h-[24px]"
                    >
                      <HomeIcon />

                      <span className="text-lg">Home</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                <SidebarMenuItem className="flex justify-center">
                  <SidebarMenuButton
                    asChild
                    className="[&>svg]:size-6 group-data-[collapsible=icon]:[&>svg]:-ml-1"
                  >
                    <Link
                      href="/main/watchlist"
                      title="Watchlist"
                    >
                      <RssIcon />

                      <span className="text-lg">Watchlist</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                <SidebarMenuItem className="flex justify-center">
                  <SidebarMenuButton
                    asChild
                    className="[&>svg]:size-6 group-data-[collapsible=icon]:[&>svg]:-ml-1"
                  >
                    <Link
                      href="/main/favorites"
                      title="Favorites"
                    >
                      <StarIcon />

                      <span className="text-lg">Favorites</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                <RoleGuard requiredRoles={['admin', 'user-approver']}>
                  <SidebarMenuItem className="flex justify-center">
                    <SidebarMenuButton
                      asChild
                      className="[&>svg]:size-6 group-data-[collapsible=icon]:[&>svg]:-ml-1"
                    >
                      <Link
                        href="/main/admin/user-approval"
                        title="User Approval"
                      >
                        <UserCheckIcon />

                        <span className="text-lg">User Approval</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </RoleGuard>

                {/* Debug page - only show in development */}
                {process.env.NODE_ENV === 'development' && (
                  <RoleGuard requiredRoles={['admin', 'user-approver']}>
                    <SidebarMenuItem className="flex justify-center">
                      <SidebarMenuButton
                        asChild
                        className="[&>svg]:size-6 group-data-[collapsible=icon]:[&>svg]:-ml-1"
                      >
                        <Link
                          href="/main/admin/debug-roles"
                          title="Debug Roles"
                        >
                          <BugIcon />

                          <span className="text-lg">Debug Roles</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </RoleGuard>
                )}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
      </Sidebar>
    </aside>
  );
}
