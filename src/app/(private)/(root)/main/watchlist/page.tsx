import { ContractBaseballCard } from '@/app/(private)/(root)/main/market/PE_HF_Ac/[marketId]/_components/contract-baseball-card';
import { Separator } from '@/components/separator';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Watchlist | HighGround',
};

// Force dynamic rendering for authenticated pages
export const dynamic = 'force-dynamic';

export default function WatchListPage() {
  return (
    <div className="watchlist-page py-16 px-8">
      <div className="page-wrapper">
        <div className="page-title">
        </div>

        <div className="wrapper mt-4 [&>div]:mt-6">
          <div className="venture-capital-widgets">
            <h2 className="font-bold mb-5">
              Venture Capital Market <Separator />
            </h2>

            <div className="widgets pl-2">
              <div className="companies-wrapper">
                <h3>Companies</h3>
              </div>
            </div>
          </div>

          <div className="product-company-widgets">
            <h2 className="font-bold mb-5">
              Product Company Market <Separator />
            </h2>

            <div className="widgets  pl-2">
              <p className="text-gray-400">Empty</p>
            </div>
          </div>

          <div className="hedge-fund-widgets">
            <h2 className="font-bold mb-5">
              Private Equity / Hedge Fund / Acquirer <Separator />
            </h2>

            <div className="widgets  pl-2">
              <div className="contract-widgets-wrapper">
                <h3>Contracts</h3>

                <ul>
                  <li>
                    <ContractBaseballCard contractTitle="Intuitive human-resource hub">
                      Intuitive human-resource hub
                    </ContractBaseballCard>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
