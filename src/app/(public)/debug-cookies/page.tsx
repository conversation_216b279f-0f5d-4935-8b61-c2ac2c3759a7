import { cookies } from 'next/headers';

export default async function DebugCookiesPage() {
  const cookieStore = await cookies();
  const allCookies = cookieStore.getAll();
  
  // Filter NextAuth-related cookies
  const nextAuthCookies = allCookies.filter(cookie => 
    cookie.name.includes('next-auth') || 
    cookie.name.includes('__Secure-next-auth') || 
    cookie.name.includes('__Host-next-auth')
  );

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Cookie Debug Page</h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">NextAuth Cookies ({nextAuthCookies.length})</h2>
        {nextAuthCookies.length === 0 ? (
          <p className="text-green-600 font-medium">✅ No NextAuth cookies found - session properly cleared!</p>
        ) : (
          <div className="space-y-2">
            {nextAuthCookies.map((cookie, index) => (
              <div key={index} className="bg-gray-100 p-3 rounded">
                <div className="font-mono text-sm">
                  <strong>Name:</strong> {cookie.name}
                </div>
                <div className="font-mono text-sm">
                  <strong>Value:</strong> {cookie.value.substring(0, 50)}...
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">All Cookies ({allCookies.length})</h2>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {allCookies.map((cookie, index) => (
            <div key={index} className="bg-gray-50 p-2 rounded text-sm">
              <span className="font-mono">{cookie.name}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="space-x-4">
        <a 
          href="/login" 
          className="inline-block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Go to Login
        </a>
        <a 
          href="/main/home" 
          className="inline-block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Go to Main (Protected)
        </a>
      </div>
    </div>
  );
}
