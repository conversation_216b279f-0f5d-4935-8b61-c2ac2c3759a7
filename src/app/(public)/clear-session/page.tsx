'use client';

import { signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function ClearSessionPage() {
  const router = useRouter();
  const [isClearing, setIsClearing] = useState(false);

  const clearAllCookies = () => {
    // Get all cookies
    const cookies = document.cookie.split(';');
    
    // Clear each cookie
    cookies.forEach(cookie => {
      const eqPos = cookie.indexOf('=');
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
      
      // Clear with different path and domain combinations
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=localhost`;
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.localhost`;
    });
  };

  const handleClearSession = async () => {
    setIsClearing(true);
    
    try {
      // Clear all cookies first
      clearAllCookies();
      
      // Clear localStorage and sessionStorage
      localStorage.clear();
      sessionStorage.clear();
      
      // Sign out from NextAuth
      await signOut({ 
        callbackUrl: '/login',
        redirect: false 
      });
      
      // Force reload to clear any cached data
      window.location.href = '/login';
    } catch (error) {
      console.error('Error clearing session:', error);
      setIsClearing(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6 text-gray-900">
          Clear Session Data
        </h1>
        
        <div className="space-y-4 mb-6">
          <p className="text-gray-600 text-sm">
            This will clear all session data including:
          </p>
          <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
            <li>All NextAuth cookies</li>
            <li>Browser localStorage</li>
            <li>Browser sessionStorage</li>
            <li>Current authentication session</li>
          </ul>
        </div>

        <div className="space-y-3">
          <button
            onClick={handleClearSession}
            disabled={isClearing}
            className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isClearing ? 'Clearing...' : 'Clear All Session Data'}
          </button>
          
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700"
          >
            Back to Login
          </button>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-yellow-800 text-sm">
            <strong>Note:</strong> Use this if you&apos;re experiencing login issues or stuck in redirect loops.
          </p>
        </div>
      </div>
    </div>
  );
}
