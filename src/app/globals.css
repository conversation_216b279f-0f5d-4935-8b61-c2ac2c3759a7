@import "tailwindcss";

@plugin "tailwindcss-animate";

@theme {
    --color-background: hsl(var(--background));
    --color-foreground: hsl(var(--foreground));
    --color-card: hsl(var(--card));
    --color-card-foreground: hsl(var(--card-foreground));
    --color-popover: hsl(var(--popover));
    --color-popover-foreground: hsl(var(--popover-foreground));
    --color-primary: hsl(var(--primary));
    --color-primary-foreground: hsl(var(--primary-foreground));
    --color-secondary: hsl(var(--secondary));
    --color-secondary-foreground: hsl(var(--secondary-foreground));
    --color-muted: hsl(var(--muted));
    --color-muted-foreground: hsl(var(--muted-foreground));
    --color-accent: hsl(var(--accent));
    --color-accent-foreground: hsl(var(--accent-foreground));
    --color-destructive: hsl(var(--destructive));
    --color-destructive-foreground: hsl(var(--destructive-foreground));
    --color-border: hsl(var(--border));
    --color-input: hsl(var(--input));
    --color-ring: hsl(var(--ring));
    --color-chart-1: hsl(var(--chart-1));
    --color-chart-2: hsl(var(--chart-2));
    --color-chart-3: hsl(var(--chart-3));
    --color-chart-4: hsl(var(--chart-4));
    --color-chart-5: hsl(var(--chart-5));
    --color-sidebar: hsl(var(--sidebar-background));
    --color-sidebar-foreground: hsl(var(--sidebar-foreground));
    --color-sidebar-primary: hsl(var(--sidebar-primary));
    --color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
    --color-sidebar-accent: hsl(var(--sidebar-accent));
    --color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
    --color-sidebar-border: hsl(var(--sidebar-border));
    --color-sidebar-ring: hsl(var(--sidebar-ring));
    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: 0 0% 9%;
        --primary-foreground: 0 0% 98%;
        --secondary: 0 0% 96.1%;
        --secondary-foreground: 0 0% 9%;
        --muted: 0 0% 96.1%;
        --muted-foreground: 0 0% 45.1%;
        --accent: 0 0% 96.1%;
        --accent-foreground: 0 0% 9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 89.8%;
        --input: 0 0% 89.8%;
        --ring: 0 0% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;

        /* Body medium font variables */
        --body-medium-font-family: "Inter", Helvetica;
        --body-medium-font-size: 14px;
        --body-medium-font-style: normal;
        --body-medium-font-weight: 500;
        --body-medium-letter-spacing: 0px;
        --body-medium-line-height: 24px;

        --sidebar-background: 218 19% 18%;
        --sidebar-foreground: 0 0% 100%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }

    .dark {
        --background: 0 0% 3.9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 0 0% 9%;
        --secondary: 0 0% 14.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 14.9%;
        --muted-foreground: 0 0% 63.9%;
        --accent: 0 0% 14.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 14.9%;
        --input: 0 0% 14.9%;
        --ring: 0 0% 83.1%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;

        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/* Layout styles for sidebar positioning */
.layout-wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
}

.page-content {
    /* Remove manual margin since sidebar is now properly sized */
    margin-left: 0;
    width: 100%;
    transition: margin-left 0.2s ease-linear, width 0.2s ease-linear;
}

/* Force sidebar width to 81px when collapsed - multiple approaches */
[data-state="collapsed"] {
    --sidebar-width-icon: 81px !important;
}

/* Target the sidebar container with data-collapsible="icon" */
[data-state="collapsed"][data-collapsible="icon"] > div:first-child,
[data-state="collapsed"][data-collapsible="icon"] > div:last-child {
    width: 81px !important;
}

/* Target by class names */
.group.peer[data-state="collapsed"] > div {
    width: 81px !important;
}

/* More specific targeting */
.group.peer.hidden.text-sidebar-foreground[data-state="collapsed"] > div {
    width: 81px !important;
}

/* Target the CSS variable usage directly */
.w-\\[--sidebar-width-icon\\] {
    width: 81px !important;
}


/* Clean approach: Use CSS custom properties for sidebar width */
:root {
    --sidebar-width-icon: 81px;
}

[data-sidebar="sidebar"][data-state="collapsed"] {
    width: var(--sidebar-width-icon);
}