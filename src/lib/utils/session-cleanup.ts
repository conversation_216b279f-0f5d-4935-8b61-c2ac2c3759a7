import { NextRequest, NextResponse } from 'next/server';

/**
 * Clears all NextAuth session cookies and redirects to login
 * This is more aggressive than the standard signOut to handle stale sessions
 */
export function clearStaleSession(request: NextRequest): NextResponse {
  console.log('Clearing stale session and redirecting to login');

  const response = NextResponse.redirect(new URL('/login', request.url));
  const hostname = request.nextUrl.hostname;
  const isProduction = process.env.NODE_ENV === 'production';

  // Get all existing cookies to find numbered session tokens
  const existingCookies = request.cookies.getAll();
  const sessionTokenCookies = existingCookies
    .filter(cookie => cookie.name.includes('session-token'))
    .map(cookie => cookie.name);

  // Base cookie names to clear
  const baseCookiesToClear = [
    'next-auth.session-token',
    '__Secure-next-auth.session-token',
    'next-auth.csrf-token',
    '__Host-next-auth.csrf-token',
    'next-auth.callback-url',
    '__Secure-next-auth.callback-url',
    'next-auth.pkce.code_verifier',
    '__Secure-next-auth.pkce.code_verifier',
    'next-auth.state',
    '__Secure-next-auth.state'
  ];

  // Combine base cookies with any numbered session token cookies found
  const allCookiesToClear = [...new Set([...baseCookiesToClear, ...sessionTokenCookies])];

  console.log('Cookies to clear:', allCookiesToClear);

  allCookiesToClear.forEach(cookieName => {
    // Strategy 1: Use NextResponse.cookies.delete()
    response.cookies.delete(cookieName);

    // Strategy 2: Set expired cookie with current domain
    response.cookies.set(cookieName, '', {
      expires: new Date(0),
      maxAge: 0,
      path: '/',
      domain: hostname,
      httpOnly: true,
      secure: isProduction,
      sameSite: 'lax'
    });

    // Strategy 3: Set expired cookie without domain (for localhost)
    response.cookies.set(cookieName, '', {
      expires: new Date(0),
      maxAge: 0,
      path: '/',
      httpOnly: true,
      secure: isProduction,
      sameSite: 'lax'
    });

    // Strategy 4: For production, also try with the app domain from env
    if (isProduction && process.env.NEXT_PUBLIC_APP_DOMAIN) {
      try {
        const appDomain = new URL(process.env.NEXT_PUBLIC_APP_DOMAIN).hostname;
        response.cookies.set(cookieName, '', {
          expires: new Date(0),
          maxAge: 0,
          path: '/',
          domain: appDomain,
          httpOnly: true,
          secure: true,
          sameSite: 'lax'
        });
      } catch (error) {
        console.warn('Failed to parse NEXT_PUBLIC_APP_DOMAIN for cookie clearing:', error);
      }
    }
  });

  // Add cache control headers to prevent caching
  response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
  response.headers.set('Pragma', 'no-cache');
  response.headers.set('Expires', '0');

  // Add a header to indicate session was cleared (for debugging)
  response.headers.set('X-Session-Cleared', 'true');

  return response;
}

/**
 * Checks if a token appears to be stale/corrupted
 */
export function isStaleToken(token: unknown): boolean {
  // No token at all
  if (!token) return true;

  // Token structure seems corrupted
  if (typeof token !== 'object') return true;

  // Type guard to check if token has the expected properties
  const tokenObj = token as Record<string, unknown>;

  // Has refresh error
  if (tokenObj.error === 'RefreshAccessTokenError') return true;

  // Missing essential fields
  if (!tokenObj.accessToken && !tokenObj.refreshToken) return true;

  // Check if refresh token is very old (more than 24 hours)
  const now = Date.now();
  const refreshTokenExpires = tokenObj.refreshTokenExpires as number;
  if (refreshTokenExpires && now > refreshTokenExpires) {
    console.log('Refresh token has expired');
    return true;
  }

  // Check if access token is very old (more than 24 hours past expiry)
  const accessTokenExpires = tokenObj.accessTokenExpires as number;
  if (accessTokenExpires) {
    const tokenAge = now - accessTokenExpires;
    const maxStaleAge = 24 * 60 * 60 * 1000; // 24 hours
    if (tokenAge > maxStaleAge) {
      console.log('Access token is too old, considering stale');
      return true;
    }
  }

  return false;
}

/**
 * Debug utility to log all NextAuth-related cookies
 * Useful for troubleshooting cookie clearing issues
 */
export function debugNextAuthCookies(request: NextRequest): void {
  const allCookies = request.cookies.getAll();
  const nextAuthCookies = allCookies.filter(cookie =>
    cookie.name.includes('next-auth') ||
    cookie.name.includes('__Secure-next-auth') ||
    cookie.name.includes('__Host-next-auth')
  );

  console.log('NextAuth cookies found:', nextAuthCookies.map(c => ({
    name: c.name,
    value: c.value.substring(0, 20) + '...', // Truncate for security
    hasValue: !!c.value
  })));
}
