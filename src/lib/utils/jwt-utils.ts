/**
 * Utility functions for working with JWT tokens from Keycloak
 */

export interface KeycloakTokenPayload {
  sub: string;
  email_verified: boolean;
  name: string;
  preferred_username: string;
  given_name: string;
  family_name: string;
  email: string;
  realm_access?: {
    roles: string[];
  };
  resource_access?: {
    [clientId: string]: {
      roles: string[];
    };
  };
  scope: string;
  sid: string;
  iat: number;
  exp: number;
  iss: string;
  aud: string;
}

/**
 * Decodes a JWT token and returns the payload
 */
export function decodeJwtToken(token: string): KeycloakTokenPayload | null {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    return null;
  }
}

/**
 * Extracts roles from a Keycloak token
 */
export function extractRolesFromToken(token: string): string[] {
  const payload = decodeJwtToken(token);
  if (!payload) return [];

  const roles: string[] = [];

  // Add realm roles
  if (payload.realm_access?.roles) {
    roles.push(...payload.realm_access.roles);
  }

  // Add client roles
  if (payload.resource_access) {
    Object.values(payload.resource_access).forEach((client) => {
      if (client.roles) {
        roles.push(...client.roles);
      }
    });
  }

  return [...new Set(roles)]; // Remove duplicates
}

/**
 * Checks if a user has a specific role
 */
export function hasRole(token: string, role: string): boolean {
  const roles = extractRolesFromToken(token);
  return roles.includes(role);
}

/**
 * Checks if a user has any of the specified roles
 */
export function hasAnyRole(token: string, roles: string[]): boolean {
  const userRoles = extractRolesFromToken(token);
  return roles.some(role => userRoles.includes(role));
}

/**
 * Checks if a user has all of the specified roles
 */
export function hasAllRoles(token: string, roles: string[]): boolean {
  const userRoles = extractRolesFromToken(token);
  return roles.every(role => userRoles.includes(role));
} 