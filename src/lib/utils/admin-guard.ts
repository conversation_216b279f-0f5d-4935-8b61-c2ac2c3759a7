import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/config/nextauth.config';
import { hasAnyRole } from '@/lib/utils/jwt-utils';
import { redirect } from 'next/navigation';

/**
 * Server-side function to check if the current user has admin access
 * Redirects to home page if user doesn't have required roles
 */
export async function checkAdminAccess(requiredRoles: string[] = ['admin', 'user-approver']): Promise<void> {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/');
  }

  if (!session.accessToken || !hasAnyRole(session.accessToken, requiredRoles)) {
    redirect('/main/home?market=VENTURE_CAPITAL');
  }
}

/**
 * Server-side function to check if the current user has admin access
 * Returns boolean instead of redirecting
 */
export async function hasAdminAccess(requiredRoles: string[] = ['admin', 'user-approver']): Promise<boolean> {
  const session = await getServerSession(authOptions);

  if (!session?.accessToken) {
    return false;
  }

  return hasAnyRole(session.accessToken, requiredRoles);
} 