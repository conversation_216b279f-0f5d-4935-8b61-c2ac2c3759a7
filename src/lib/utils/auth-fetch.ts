'use client';

import { getSession, signOut } from 'next-auth/react';

/**
 * Enhanced fetch wrapper that handles token refresh automatically
 * Works with the middleware approach by triggering session refresh
 */
export async function authFetch(
  url: string,
  options: RequestInit = {},
): Promise<Response> {
  // Get current session
  let session = await getSession();

  if (!session?.accessToken) {
    throw new Error('No access token available');
  }

  // Add authorization header
  const headers = {
    ...options.headers,
    Authorization: `Bearer ${session.accessToken}`,
  };

  // Make the request
  let response = await fetch(url, {
    ...options,
    headers,
  });

  // If 401, try to refresh the session and retry once
  if (response.status === 401) {
    console.log('Got 401, attempting to refresh session...');

    // Force session refresh by making a request to session endpoint
    try {
      const refreshResponse = await fetch('/api/auth/session', {
        method: 'GET',
        credentials: 'include',
      });

      if (refreshResponse.ok) {
        // Get the updated session
        session = await getSession();

        if (session?.accessToken && !session.error) {
          console.log('Session refreshed, retrying request...');

          // Retry the original request with new token
          response = await fetch(url, {
            ...options,
            headers: {
              ...options.headers,
              Authorization: `Bearer ${session.accessToken}`,
            },
          });

          // If still 401 after refresh, sign out
          if (response.status === 401) {
            console.log('Still 401 after refresh, signing out...');
            await signOut({ callbackUrl: '/login' });
            throw new Error('Authentication failed after token refresh');
          }
        } else {
          console.log('Session refresh failed or has error, signing out...');
          await signOut({ callbackUrl: '/login' });
          throw new Error('Session refresh failed');
        }
      } else {
        console.log('Failed to refresh session, signing out...');
        await signOut({ callbackUrl: '/login' });
        throw new Error('Failed to refresh session');
      }
    } catch (error) {
      console.error('Error during session refresh:', error);
      await signOut({ callbackUrl: '/login' });
      throw error;
    }
  }

  return response;
}

/**
 * Convenience wrapper for JSON API calls
 */
export async function authFetchJson<T>(
  url: string,
  options: RequestInit = {},
): Promise<T> {
  const response = await authFetch(url, options);

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
}

/**
 * POST request with JSON body
 */
export async function authPost<T>(url: string, data: unknown): Promise<T> {
  return authFetchJson<T>(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
}

/**
 * PUT request with JSON body
 */
export async function authPut<T>(url: string, data: unknown): Promise<T> {
  return authFetchJson<T>(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
}

/**
 * PATCH request with JSON body
 */
export async function authPatch<T>(url: string, data: unknown): Promise<T> {
  return authFetchJson<T>(url, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
}

/**
 * DELETE request with JSON body
 */
export async function authDelete<T>(url: string, data: unknown): Promise<T> {
  return authFetchJson<T>(url, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
}

/**
 * GET request with JSON body
 */
export async function authGet<T>(
  url: string,
  params?: Record<string, string | number>,
): Promise<T> {
  const finalUrl =
    params && Object.entries(params).length > 0
      ? `${url}?${toQueryString(params)}`
      : url;

  return authFetchJson<T>(finalUrl, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

export function toQueryString(
  params: Record<string, string | number | boolean | undefined | null>,
): string {
  return Object.entries(params)
    .filter(([, value]) => value !== undefined && value !== null)
    .map(
      ([key, value]) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`,
    )
    .join('&');
}
