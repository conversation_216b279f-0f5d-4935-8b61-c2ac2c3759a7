'use client';

import { useSession } from 'next-auth/react';
import { useMemo } from 'react';

/**
 * Hook to check if the current user has specific roles
 */
export function useRoleCheck(requiredRoles: string[], requireAll = false): {
  hasAccess: boolean;
  isLoading: boolean;
  userRoles: string[];
} {
  const { data: session, status } = useSession();

  const userRoles = useMemo(() => {
    return session?.user?.roles || [];
  }, [session?.user?.roles]);

  const hasAccess = useMemo(() => {
    if (status === 'loading' || !userRoles.length) {
      return false;
    }

    if (requireAll) {
      return requiredRoles.every(role => userRoles.includes(role));
    } else {
      return requiredRoles.some(role => userRoles.includes(role));
    }
  }, [userRoles, requiredRoles, requireAll, status]);

  return {
    hasAccess,
    isLoading: status === 'loading',
    userRoles,
  };
}

/**
 * Hook to check if the current user has admin access
 */
export function useAdminAccess(): {
  hasAccess: boolean;
  isLoading: boolean;
  userRoles: string[];
} {
  return useRoleCheck(['admin', 'user-approver']);
}

/**
 * Hook to check if the current user has a specific role
 */
export function useHasRole(role: string): {
  hasAccess: boolean;
  isLoading: boolean;
  userRoles: string[];
} {
  return useRoleCheck([role]);
} 