'use client';

import useSWRImmutable from 'swr/immutable';
import { authFetchJson } from '@/lib/utils/auth-fetch';

export function useSwrAuth<T>(url: string | null) {
  const { data, isLoading, error } = useSWRImmutable(
    url, // SWR automatically handles null URLs by not making requests
    url ? (url) => authFetchJson<T>(url) : null,
    {
      // Add retry configuration for failed requests
      errorRetryCount: 2,
      errorRetryInterval: 1000, // 1 second between retries
    },
  );

  return { data, isLoading, error };
}
