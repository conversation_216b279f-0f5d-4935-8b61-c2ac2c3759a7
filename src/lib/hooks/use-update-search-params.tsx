'use client';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';

type ListAction = 'add' | 'remove' | 'toggle' | 'removeAllAndAdd';

export function useSearchParamsManager() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const getParams = () => new URLSearchParams(searchParams.toString());

  const applyParams = (params: URLSearchParams) => {
    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  };

  const setParam = (key: string, value: string | null) => {
    const params = getParams();
    if (value) {
      params.set(key, value);
    } else {
      params.delete(key);
    }

    applyParams(params);
  };

  const updateListParam = (
    key: string,
    value: string | string[],
    action: ListAction,
  ) => {
    const params = getParams();
    const items = params.get(key)?.split(',').filter(Boolean) ?? [];

    const updated = new Set(items);

    if (action === 'removeAllAndAdd') {
      updated.clear();
    }

    const valueToCheck = typeof value === 'string' ? [value] : value;

    valueToCheck.forEach((item) => {
      if (action === 'add') {
        updated.add(item);
      }

      if (action === 'remove') {
        updated.delete(item);
      }

      if (action === 'removeAllAndAdd') {
        updated.add(item);
      }

      if (action === 'toggle') {
        if (updated.has(item)) {
          updated.delete(item);
        } else {
          updated.add(item);
        }
      }
    });

    const newList = Array.from(updated);

    if (newList.length) {
      params.set(key, newList.join(','));
    } else {
      params.delete(key);
    }

    applyParams(params);
  };

  const getParam = (key: string): string | null => {
    return searchParams.get(key);
  };

  const getListParam = (key: string): string[] => {
    return searchParams.get(key)?.split(',').filter(Boolean) ?? [];
  };

  const batchUpdateParams = (callback: (params: URLSearchParams) => void) => {
    const params = getParams();
    callback(params);
    applyParams(params);
  };

  return {
    setParam,
    updateListParam,
    getParam,
    getListParam,
    batchUpdateParams,
  };
}
