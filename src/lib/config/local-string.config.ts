export const USACurrency: Intl.NumberFormatOptions = {
  style: 'currency',
  currency: 'USD',
  notation: 'compact',
};

export function formatCurrencyHumanReadable(number: number) {
  let value,
    suffix = '';

  if (number >= 1_000_000_000_000) {
    value = number / 1_000_000_000_000;
    suffix = 'Trillion';
  } else if (number >= 1_000_000_000) {
    value = number / 1_000_000_000;
    suffix = 'Billion';
  } else if (number >= 1_000_000) {
    value = number / 1_000_000;
    suffix = 'Million';
  } else {
    value = number;
  }

  const formatted = value.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 1,
  });

  const pluralizedSuffix = suffix
    ? `${suffix}${parseFloat(value.toFixed(1)) === 1 ? '' : 's'}`
    : '';

  return `$${formatted}${pluralizedSuffix ? ' ' + pluralizedSuffix : ''}`;
}
