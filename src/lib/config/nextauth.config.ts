import { AuthOptions } from 'next-auth';
import KeycloakProvider from 'next-auth/providers/keycloak';
import { JWT } from 'next-auth/jwt';
import { extractRolesFromToken } from '@/lib/utils/jwt-utils';

/**
 * Takes a token, and returns a new token with updated
 * `accessToken` and `accessTokenExpires`. If an error occurs,
 * returns the old token and an error property
 */
export async function refreshAccessToken(token: JWT): Promise<JWT> {
  try {
    const url = `${process.env.KEYCLOAK_ISSUER}/protocol/openid-connect/token`;

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      method: 'POST',
      body: new URLSearchParams({
        client_id: process.env.KEYCLOAK_CLIENT_ID!,
        client_secret: process.env.KEYCLOAK_CLIENT_SECRET!,
        grant_type: 'refresh_token',
        refresh_token: token.refreshToken as string,
      }),
    });

    const refreshedTokens = await response.json();

    if (!response.ok) {
      // Log specific error for debugging
      if (refreshedTokens.error === 'invalid_grant' && refreshedTokens.error_description === 'Token is not active') {
        console.log('Refresh token has expired or is invalid, user needs to re-authenticate');
      } else {
        console.error('Error refreshing access token:', refreshedTokens);
      }
      throw refreshedTokens;
    }

    // Clear any previous errors on successful refresh
    return {
      ...token,
      accessToken: refreshedTokens.access_token,
      accessTokenExpires: Date.now() + (refreshedTokens.expires_in * 1000),
      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken, // Fall back to old refresh token
      refreshTokenExpires: refreshedTokens.refresh_expires_in ? Date.now() + (refreshedTokens.refresh_expires_in * 1000) : token.refreshTokenExpires,
      expiresIn: refreshedTokens.expires_in, // Update the expires_in value
      error: undefined, // Clear any previous errors
    };
  } catch {
    return {
      ...token,
      error: 'RefreshAccessTokenError',
    };
  }
}

export const authOptions: AuthOptions = {
  secret: process.env.NEXTAUTH_SECRET ?? '',
  session: {
    strategy: 'jwt',
    // Align with Keycloak refresh token lifetime (typically 30 minutes)
    // This ensures session expires when refresh token expires
    maxAge: 60 * 30, // 30 minutes (matches typical Keycloak refresh token lifetime)
  },
  providers: [
    KeycloakProvider({
      clientId: process.env.KEYCLOAK_CLIENT_ID ?? '',
      clientSecret: process.env.KEYCLOAK_CLIENT_SECRET ?? '',
      issuer: process.env.KEYCLOAK_ISSUER ?? '',
    }),
  ],
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === 'production'
        ? '__Secure-next-auth.session-token'
        : 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        domain: process.env.NODE_ENV === 'production'
          ? process.env.NEXT_PUBLIC_APP_DOMAIN
            ? new URL(process.env.NEXT_PUBLIC_APP_DOMAIN).hostname
            : undefined
          : undefined,
      },
    },
    callbackUrl: {
      name: process.env.NODE_ENV === 'production'
        ? '__Secure-next-auth.callback-url'
        : 'next-auth.callback-url',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        domain: process.env.NODE_ENV === 'production'
          ? process.env.NEXT_PUBLIC_APP_DOMAIN
            ? new URL(process.env.NEXT_PUBLIC_APP_DOMAIN).hostname
            : undefined
          : undefined,
      },
    },
    csrfToken: {
      name: process.env.NODE_ENV === 'production'
        ? '__Host-next-auth.csrf-token'
        : 'next-auth.csrf-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
  callbacks: {
    async jwt({ token, user, account }): Promise<JWT> {
      // Initial sign in
      if (account && user) {
        console.log('Initial sign in, creating new token');
        console.log('Account data:', {
          access_token: account.access_token ? 'present' : 'missing',
          refresh_token: account.refresh_token ? 'present' : 'missing',
          expires_in: account.expires_in,
          refresh_expires_in: account.refresh_expires_in,
          id_token: account.id_token ? 'present' : 'missing'
        });

        // Validate expires_in value with fallback
        let expiresIn = account.expires_in as number;
        if (!expiresIn || expiresIn <= 0 || isNaN(expiresIn)) {
          console.warn('Invalid expires_in value from account:', expiresIn, 'using default 3600 seconds');
          expiresIn = 3600; // Default to 1 hour if invalid
        }

        // Extract roles from access token
        let roles: string[] = [];
        if (account.access_token) {
          try {
            roles = extractRolesFromToken(account.access_token);
            console.log('Extracted roles from token:', roles);
          } catch (error) {
            console.error('Error extracting roles from token:', error);
          }
        }

        const accessTokenExpires = Date.now() + (expiresIn * 1000);
        const newToken = {
          ...token,
          accessToken: account.access_token,
          accessTokenExpires,
          refreshToken: account.refresh_token,
          refreshTokenExpires: account.refresh_expires_in ? Date.now() + ((account.refresh_expires_in as number) * 1000) : undefined,
          expiresIn: expiresIn, // Store the original expires_in value
          id_token: account.id_token,
          user,
          roles,
          error: undefined, // Clear any previous errors
        } as JWT;

        console.log('New token created with expiry:', new Date(accessTokenExpires).toISOString());
        console.log('Token expires in seconds:', expiresIn);
        return newToken;
      }

      // Validate token structure
      if (!token.accessTokenExpires || !token.accessToken) {
        console.log('Invalid token structure, marking for refresh');
        return {
          ...token,
          error: 'RefreshAccessTokenError',
        } as JWT;
      }

      // Return previous token if the access token has not expired yet
      // Add 1-minute buffer before expiry to proactively refresh tokens
      const bufferTime = 60 * 1000;
      if (Date.now() < ((token.accessTokenExpires as number) - bufferTime)) {
        return token;
      }

      // Check if token is too old to refresh (prevent very old tokens from causing issues)
      const tokenAge = Date.now() - (token.accessTokenExpires as number);
      const maxRefreshAge = 24 * 60 * 60 * 1000; // 24 hours
      if (tokenAge > maxRefreshAge) {
        console.log(`Access token expired ${Math.round(tokenAge / 1000)}s ago, too old to refresh`);
        return {
          ...token,
          error: 'RefreshAccessTokenError',
        } as JWT;
      }

      // Access token has expired, try to update it
      console.log('Access token expired, attempting refresh in JWT callback');
      try {
        const refreshedToken = await refreshAccessToken(token);

        // If refresh failed, return token with error
        if (refreshedToken.error) {
          console.log('Token refresh failed in JWT callback:', refreshedToken.error);
          return {
            ...token,
            error: refreshedToken.error,
          } as JWT;
        }

        // Extract roles from refreshed token
        let roles: string[] = [];
        if (refreshedToken.accessToken) {
          try {
            roles = extractRolesFromToken(refreshedToken.accessToken);
            console.log('Extracted roles from refreshed token:', roles);
          } catch (error) {
            console.error('Error extracting roles from refreshed token:', error);
          }
        }

        console.log('Token refreshed successfully in JWT callback');
        return {
          ...refreshedToken,
          roles,
        };
      } catch (error) {
        console.error('Error in JWT callback refresh:', error);
        return {
          ...token,
          error: 'RefreshAccessTokenError',
        } as JWT;
      }
    },
    async session({ session, token }) {
      if (token) {
        if (token.user) {
          session.user = {
            ...token.user,
            roles: token.roles,
          };
        }
        session.accessToken = token.accessToken;
        session.id_token = token.id_token;
        session.error = token.error;
      }

      return session;
    },
  },
  pages: {
    signIn: '/login',
  },
};
