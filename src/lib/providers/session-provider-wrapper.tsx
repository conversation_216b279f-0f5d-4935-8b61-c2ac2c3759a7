'use client';

import { SessionProvider } from 'next-auth/react';
import { ReactNode } from 'react';
import { Session } from 'next-auth';

interface SessionProviderWrapperProps {
  children: ReactNode;
  session?: Session | null;
}

export default function SessionProviderWrapper({
  children,
  session
}: SessionProviderWrapperProps) {
  return (
    <SessionProvider
      session={session}
      // Refetch session every 4 minutes to keep it fresh
      // This is calculated with 1 minute buffer time (5 min token - 1 min buffer)
      refetchInterval={4 * 60}
      // Refetch on window focus
      refetchOnWindowFocus={true}
    >
      {children}
    </SessionProvider>
  );
}
