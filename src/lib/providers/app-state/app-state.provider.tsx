'use client';

import React, {
  createContext,
  useReducer,
  useContext,
  ReactNode,
  useEffect,
} from 'react';
import { Favorite } from '@/lib/types/favorite.type';
import {
  AppState,
  AppStateAction,
  AppStateContext,
} from './types/app-state.type';

export const initialState: AppState = {
  favorites: undefined,
};

export const appReducer = (
  state: AppState,
  action: AppStateAction,
): AppState => {
  switch (action.type) {
    case 'SET_FAVORITES':
      return {
        ...state,
        favorites: action.payload,
      };
    default:
      return state;
  }
};

const AppContext = createContext<AppStateContext | undefined>(undefined);

export function AppStateProvider({
  children,
  favorites,
}: {
  children: ReactNode;
  favorites: Record<string, Favorite[]>;
}) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  useEffect(() => {
    dispatch({ payload: favorites, type: 'SET_FAVORITES' });
  }, [favorites]);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

export function useAppState(): AppStateContext {
  const context = useContext(AppContext);

  if (!context) {
    throw new Error('AppContext should be within GlobalProvider');
  }

  return context;
}
