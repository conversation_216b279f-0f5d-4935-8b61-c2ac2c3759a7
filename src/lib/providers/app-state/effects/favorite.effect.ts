import { config } from '@/lib/config';
import { useSWRMutateWithAuth } from '@/lib/hooks/use-swr-mutate-auth';
import { useEffect } from 'react';
import { Favorite } from '@/lib/types/favorite.type';
import { useAppState } from '@/lib/providers/app-state/app-state.provider';

export function useRefreshAppFavorites() {
  const { dispatch } = useAppState();
  const configBaseUrl = config.api.base_url;

  const { trigger, data, isMutating } = useSWRMutateWithAuth(
    `${configBaseUrl}/dashboard/favorite`,
    'GET',
  );

  useEffect(() => {
    if (data) {
      dispatch({ type: 'SET_FAVORITES', payload: data as Record<string, Favorite[]> });
    }
  }, [data]);// eslint-disable-line

  return { getUserFavorites: () => trigger(), refreshingFavorites: isMutating };
}
