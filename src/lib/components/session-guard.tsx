'use client';

import { signIn, useSession } from 'next-auth/react';
import { ReactNode, useEffect } from 'react';

interface SessionGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * SessionGuard component that monitors session state and handles authentication errors.
 * Automatically triggers re-authentication when RefreshAccessTokenError occurs.
 */
export function SessionGuard({ children, fallback }: SessionGuardProps) {
  const { data: session, status } = useSession();

  useEffect(() => {
    // Handle refresh token errors by triggering re-authentication
    if (session?.error === 'RefreshAccessTokenError') {
      console.log('RefreshAccessTokenError detected, triggering re-authentication');
      signIn('keycloak');
    }
  }, [session]);

  // Show loading state while session is being fetched
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        {fallback || (
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading session...</p>
          </div>
        )}
      </div>
    );
  }

  // Show error state when there's a refresh token error
  if (session?.error === 'RefreshAccessTokenError') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2 text-red-600">Session Expired</h2>
          <p className="text-gray-600 mb-4">
            Your session has expired. You will be redirected to sign in.
          </p>
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600 mx-auto"></div>
        </div>
      </div>
    );
  }

  // Render children if session is valid
  return <>{children}</>;
}
