'use client';

import { ReactNode } from 'react';
import { useRoleCheck } from '@/lib/hooks/use-role-check';

interface RoleGuardProps {
  children: ReactNode;
  requiredRoles: string[];
  fallback?: ReactNode;
  requireAll?: boolean; // If true, user must have ALL roles. If false, user must have ANY role.
}

/**
 * RoleGuard component that conditionally renders content based on user roles.
 * Requires the user to have at least one of the specified roles (or all if requireAll is true).
 */
export function RoleGuard({ 
  children, 
  requiredRoles, 
  fallback = null,
  requireAll = false 
}: RoleGuardProps) {
  const { hasAccess, isLoading } = useRoleCheck(requiredRoles, requireAll);

  // Show loading state while session is being fetched
  if (isLoading) {
    return null;
  }

  return hasAccess ? <>{children}</> : fallback;
}

/**
 * Hook to check if the current user has specific roles
 * @deprecated Use useRoleCheck from '@/lib/hooks/use-role-check' instead
 */
export function useHasRole(requiredRoles: string[], requireAll = false): boolean {
  const { hasAccess } = useRoleCheck(requiredRoles, requireAll);
  return hasAccess;
} 