import { DefaultSession, DefaultUser } from 'next-auth';

declare module 'next-auth' {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: {
      id: string;
      roles?: string[];
    } & DefaultSession['user'];
    accessToken?: string;
    id_token?: string;
    error?: string;
  }

  interface User extends DefaultUser {
    id: string;
    roles?: string[];
  }
}

declare module 'next-auth/jwt' {
  /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */
  interface JWT {
    accessToken?: string;
    accessTokenExpires?: number;
    refreshToken?: string;
    refreshTokenExpires?: number;
    expiresIn?: number;
    id_token?: string;
    user?: DefaultUser;
    error?: string;
    roles?: string[];
  }
}
