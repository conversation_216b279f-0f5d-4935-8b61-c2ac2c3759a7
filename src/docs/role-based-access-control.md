# Role-Based Access Control (RBAC) Implementation

This document describes the role-based access control implementation for the HighGround MVP frontend application.

## Overview

The application uses Keycloak for authentication and implements role-based access control to restrict access to admin features like user approval. Users must have specific roles assigned in Keycloak to access these features.

## Required Roles

The following roles are required for admin access:

- `admin` - Full administrative access
- `user-approver` - Access to user approval functionality

Users with either role can access the user approval features.

## Implementation Details

### 1. JWT Token Processing

Roles are extracted from the Keycloak JWT access token in the NextAuth configuration:

```typescript
// src/lib/config/nextauth.config.ts
import { extractRolesFromToken } from '@/lib/utils/jwt-utils';

// In the JWT callback
if (account.access_token) {
  try {
    roles = extractRolesFromToken(account.access_token);
  } catch (error) {
    console.error('Error extracting roles from token:', error);
  }
}
```

### 2. Type Definitions

The NextAuth types have been extended to include roles:

```typescript
// src/lib/types/next-auth.d.ts
interface Session {
  user: {
    id: string;
    roles?: string[];
  } & DefaultSession['user'];
  // ...
}
```

### 3. Role Utilities

Utility functions for working with JWT tokens and roles:

```typescript
// src/lib/utils/jwt-utils.ts
export function extractRolesFromToken(token: string): string[]
export function hasRole(token: string, role: string): boolean
export function hasAnyRole(token: string, roles: string[]): boolean
export function hasAllRoles(token: string, roles: string[]): boolean
```

### 4. Client-Side Role Checking

#### RoleGuard Component

A React component that conditionally renders content based on user roles:

```typescript
import { RoleGuard } from '@/lib/components/role-guard';

<RoleGuard requiredRoles={['admin', 'user-approver']}>
  <AdminContent />
</RoleGuard>
```

#### useRoleCheck Hook

A custom hook for checking roles in components:

```typescript
import { useRoleCheck } from '@/lib/hooks/use-role-check';

const { hasAccess, isLoading, userRoles } = useRoleCheck(['admin', 'user-approver']);
```

### 5. Server-Side Role Checking

Utility functions for server-side role validation:

```typescript
// src/lib/utils/admin-guard.ts
export async function checkAdminAccess(requiredRoles?: string[]): Promise<void>
export async function hasAdminAccess(requiredRoles?: string[]): Promise<boolean>
```

## Usage Examples

### Protecting UI Components

```typescript
// Show admin menu only to users with admin roles
<RoleGuard requiredRoles={['admin', 'user-approver']}>
  <SidebarMenuItem>
    <Link href="/main/admin/user-approval">User Approval</Link>
  </SidebarMenuItem>
</RoleGuard>
```

### Protecting Server Actions

```typescript
// In a server action
export async function adminAction() {
  await checkAdminAccess(); // Will redirect if user doesn't have access
  
  // Proceed with admin logic
}
```

### Protecting Pages

```typescript
// In a page component
export default async function AdminPage() {
  await checkAdminAccess(); // Will redirect if user doesn't have access
  
  return <AdminContent />;
}
```

### Conditional Rendering

```typescript
// In a component
const { hasAccess } = useRoleCheck(['admin']);

return (
  <div>
    {hasAccess && <AdminButton />}
  </div>
);
```

## Keycloak Configuration

To enable role-based access control, ensure your Keycloak setup includes:

1. **Realm Roles**: Create `admin` and `user-approver` roles in your Keycloak realm
2. **Client Roles**: Optionally create client-specific roles
3. **User Assignment**: Assign appropriate roles to users
4. **Token Configuration**: Ensure roles are included in access tokens

### Example Keycloak Role Setup

1. Go to your Keycloak Admin Console
2. Navigate to your realm
3. Go to "Roles" in the left sidebar
4. Create the following roles:
   - `admin` - Full administrative access
   - `user-approver` - User approval access
5. Assign roles to users as needed

## Debugging

A debug page is available at `/main/admin/debug-roles` (development only) that shows:

- Current user information
- Extracted roles from JWT token
- Session roles from NextAuth
- Access control status
- Testing links

## Security Considerations

1. **Server-Side Validation**: Always validate roles on the server side, not just client side
2. **Token Expiration**: Roles are re-evaluated when tokens are refreshed
3. **Role Changes**: Role changes in Keycloak require user re-authentication to take effect
4. **Audit Logging**: Consider logging role-based access attempts for security monitoring

## Troubleshooting

### Common Issues

1. **No roles showing**: Check that roles are properly assigned in Keycloak and included in access tokens
2. **Access denied unexpectedly**: Verify the role names match exactly (case-sensitive)
3. **Roles not updating**: Users may need to re-authenticate after role changes

### Debug Steps

1. Visit `/main/admin/debug-roles` to see current role information
2. Check browser console for role extraction errors
3. Verify Keycloak token contains expected roles
4. Ensure NextAuth session includes roles

## Future Enhancements

Potential improvements to consider:

1. **Role Hierarchy**: Implement role inheritance
2. **Permission System**: More granular permissions beyond roles
3. **Dynamic Role Loading**: Load roles from API instead of JWT
4. **Role Caching**: Cache role information for performance
5. **Audit Trail**: Log role-based access for compliance 